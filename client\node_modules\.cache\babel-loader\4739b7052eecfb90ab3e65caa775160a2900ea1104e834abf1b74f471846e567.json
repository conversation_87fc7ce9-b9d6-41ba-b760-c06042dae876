{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { millisecondsInMinute } from \"./constants.js\";\nimport { differenceInMilliseconds } from \"./differenceInMilliseconds.js\";\n\n/**\n * The {@link differenceInMinutes} function options.\n */\n\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of minutes\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport function differenceInMinutes(dateLeft, dateRight, options) {\n  const diff = differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInMinutes;", "map": {"version": 3, "names": ["getRoundingMethod", "millisecondsInMinute", "differenceInMilliseconds", "differenceInMinutes", "dateLeft", "dateRight", "options", "diff", "roundingMethod"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/differenceInMinutes.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { millisecondsInMinute } from \"./constants.js\";\nimport { differenceInMilliseconds } from \"./differenceInMilliseconds.js\";\n\n/**\n * The {@link differenceInMinutes} function options.\n */\n\n/**\n * @name differenceInMinutes\n * @category Minute Helpers\n * @summary Get the number of minutes between the given dates.\n *\n * @description\n * Get the signed number of full (rounded towards 0) minutes between the given dates.\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of minutes\n *\n * @example\n * // How many minutes are between 2 July 2014 12:07:59 and 2 July 2014 12:20:00?\n * const result = differenceInMinutes(\n *   new Date(2014, 6, 2, 12, 20, 0),\n *   new Date(2014, 6, 2, 12, 7, 59)\n * )\n * //=> 12\n *\n * @example\n * // How many minutes are between 10:01:59 and 10:00:00\n * const result = differenceInMinutes(\n *   new Date(2000, 0, 1, 10, 0, 0),\n *   new Date(2000, 0, 1, 10, 1, 59)\n * )\n * //=> -1\n */\nexport function differenceInMinutes(dateLeft, dateRight, options) {\n  const diff =\n    differenceInMilliseconds(dateLeft, dateRight) / millisecondsInMinute;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInMinutes;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,wBAAwB,QAAQ,+BAA+B;;AAExE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAChE,MAAMC,IAAI,GACRL,wBAAwB,CAACE,QAAQ,EAAEC,SAAS,CAAC,GAAGJ,oBAAoB;EACtE,OAAOD,iBAAiB,CAACM,OAAO,EAAEE,cAAc,CAAC,CAACD,IAAI,CAAC;AACzD;;AAEA;AACA,eAAeJ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}