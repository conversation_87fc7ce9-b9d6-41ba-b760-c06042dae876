import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Popover,
  Paper,
  Tooltip,
  Chip,
  Typography,
} from '@mui/material';
import {
  SendOutlined,
  AttachFileOutlined,
  EmojiEmotionsOutlined,
  FormatBoldOutlined,
  FormatItalicOutlined,
  CodeOutlined,
  CloseOutlined,
} from '@mui/icons-material';
import EmojiPicker, { EmojiClickData } from 'emoji-picker-react';

import { useTyping } from '../hooks';
import { Message } from '../types';

interface MessageInputProps {
  onSendMessage: (content: string, replyTo?: string) => void;
  placeholder?: string;
  roomId?: string;
  replyToMessage?: Message | null;
  onCancelReply?: () => void;
  disabled?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  placeholder = 'Type a message...',
  roomId,
  replyToMessage,
  onCancelReply,
  disabled = false,
}) => {
  const [messageText, setMessageText] = useState('');
  const [emojiAnchorEl, setEmojiAnchorEl] = useState<HTMLElement | null>(null);
  const [selectionStart, setSelectionStart] = useState(0);
  const [selectionEnd, setSelectionEnd] = useState(0);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { startTyping, stopTyping } = useTyping(roomId);

  const handleSendMessage = useCallback(() => {
    if (!messageText.trim() || disabled) return;

    const content = messageText.trim();
    setMessageText('');
    stopTyping();

    onSendMessage(content, replyToMessage?.id);
  }, [messageText, disabled, stopTyping, onSendMessage, replyToMessage]);

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setMessageText(value);
    
    // Store cursor position
    setSelectionStart(event.target.selectionStart || 0);
    setSelectionEnd(event.target.selectionEnd || 0);
    
    if (value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    const emoji = emojiData.emoji;
    const newText = messageText.slice(0, selectionStart) + emoji + messageText.slice(selectionEnd);
    setMessageText(newText);
    
    // Focus back to input and set cursor position
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        const newPosition = selectionStart + emoji.length;
        inputRef.current.setSelectionRange(newPosition, newPosition);
      }
    }, 0);
    
    setEmojiAnchorEl(null);
  };

  const handleEmojiButtonClick = (event: React.MouseEvent<HTMLElement>) => {
    setEmojiAnchorEl(event.currentTarget);
    
    // Store current cursor position
    if (inputRef.current) {
      setSelectionStart(inputRef.current.selectionStart || 0);
      setSelectionEnd(inputRef.current.selectionEnd || 0);
    }
  };

  const handleFormatText = (format: 'bold' | 'italic' | 'code') => {
    if (!inputRef.current) return;
    
    const start = inputRef.current.selectionStart || 0;
    const end = inputRef.current.selectionEnd || 0;
    const selectedText = messageText.slice(start, end);
    
    let formattedText = '';
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'code':
        formattedText = `\`${selectedText}\``;
        break;
    }
    
    const newText = messageText.slice(0, start) + formattedText + messageText.slice(end);
    setMessageText(newText);
    
    // Set cursor position after formatting
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
        const newPosition = start + formattedText.length;
        inputRef.current.setSelectionRange(newPosition, newPosition);
      }
    }, 0);
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // TODO: Implement file upload functionality
      console.log('File selected:', files[0]);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Reply Preview */}
      {replyToMessage && (
        <Box
          sx={{
            mb: 1,
            p: 1,
            bgcolor: 'grey.100',
            borderRadius: 1,
            borderLeft: 3,
            borderColor: 'primary.main',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box>
            <Typography variant="caption" color="primary" fontWeight="bold">
              Replying to {replyToMessage.sender.username}
            </Typography>
            <Typography variant="body2" noWrap sx={{ maxWidth: '300px' }}>
              {replyToMessage.content}
            </Typography>
          </Box>
          <IconButton size="small" onClick={onCancelReply}>
            <CloseOutlined fontSize="small" />
          </IconButton>
        </Box>
      )}

      {/* Message Input */}
      <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}>
        {/* Formatting Buttons */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Tooltip title="Bold">
            <IconButton size="small" onClick={() => handleFormatText('bold')}>
              <FormatBoldOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Italic">
            <IconButton size="small" onClick={() => handleFormatText('italic')}>
              <FormatItalicOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Code">
            <IconButton size="small" onClick={() => handleFormatText('code')}>
              <CodeOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>

        {/* File Upload */}
        <Tooltip title="Attach File">
          <IconButton size="small" color="primary" onClick={handleFileUpload}>
            <AttachFileOutlined />
          </IconButton>
        </Tooltip>
        
        {/* Text Input */}
        <TextField
          ref={inputRef}
          fullWidth
          multiline
          maxRows={4}
          placeholder={placeholder}
          value={messageText}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          disabled={disabled}
          variant="outlined"
          size="small"
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 3,
            },
          }}
        />
        
        {/* Emoji Picker */}
        <Tooltip title="Add Emoji">
          <IconButton size="small" color="primary" onClick={handleEmojiButtonClick}>
            <EmojiEmotionsOutlined />
          </IconButton>
        </Tooltip>
        
        {/* Send Button */}
        <IconButton
          color="primary"
          onClick={handleSendMessage}
          disabled={!messageText.trim() || disabled}
          sx={{
            bgcolor: messageText.trim() && !disabled ? 'primary.main' : 'transparent',
            color: messageText.trim() && !disabled ? 'primary.contrastText' : 'primary.main',
            '&:hover': {
              bgcolor: messageText.trim() && !disabled ? 'primary.dark' : 'primary.light',
            },
          }}
        >
          <SendOutlined />
        </IconButton>
      </Box>

      {/* Emoji Picker Popover */}
      <Popover
        open={Boolean(emojiAnchorEl)}
        anchorEl={emojiAnchorEl}
        onClose={() => setEmojiAnchorEl(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
      >
        <Paper sx={{ p: 1 }}>
          <EmojiPicker
            onEmojiClick={handleEmojiClick}
            width={350}
            height={400}
            previewConfig={{ showPreview: false }}
          />
        </Paper>
      </Popover>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        hidden
        onChange={handleFileSelect}
        accept="image/*,video/*,.pdf,.doc,.docx,.txt"
      />
    </Box>
  );
};

export default MessageInput;
