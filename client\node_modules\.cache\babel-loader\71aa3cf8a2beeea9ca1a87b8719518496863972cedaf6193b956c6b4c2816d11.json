{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * The {@link formatDuration} function options.\n */\n\nconst defaultFormat = [\"years\", \"months\", \"weeks\", \"days\", \"hours\", \"minutes\", \"seconds\"];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param duration - The duration to format\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport function formatDuration(duration, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const format = options?.format ?? defaultFormat;\n  const zero = options?.zero ?? false;\n  const delimiter = options?.delimiter ?? \" \";\n  if (!locale.formatDistance) {\n    return \"\";\n  }\n  const result = format.reduce((acc, unit) => {\n    const token = `x${unit.replace(/(^.)/, m => m.toUpperCase())}`;\n    const value = duration[unit];\n    if (value !== undefined && (zero || duration[unit])) {\n      return acc.concat(locale.formatDistance(token, value));\n    }\n    return acc;\n  }, []).join(delimiter);\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatDuration;", "map": {"version": 3, "names": ["defaultLocale", "getDefaultOptions", "defaultFormat", "formatDuration", "duration", "options", "defaultOptions", "locale", "format", "zero", "delimiter", "formatDistance", "result", "reduce", "acc", "unit", "token", "replace", "m", "toUpperCase", "value", "undefined", "concat", "join"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/formatDuration.js"], "sourcesContent": ["import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * The {@link formatDuration} function options.\n */\n\nconst defaultFormat = [\n  \"years\",\n  \"months\",\n  \"weeks\",\n  \"days\",\n  \"hours\",\n  \"minutes\",\n  \"seconds\",\n];\n\n/**\n * @name formatDuration\n * @category Common Helpers\n * @summary Formats a duration in human-readable format\n *\n * @description\n * Return human-readable duration string i.e. \"9 months 2 days\"\n *\n * @param duration - The duration to format\n * @param options - An object with options.\n *\n * @returns The formatted date string\n *\n * @example\n * // Format full duration\n * formatDuration({\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> '2 years 9 months 1 week 7 days 5 hours 9 minutes 30 seconds'\n *\n * @example\n * // Format partial duration\n * formatDuration({ months: 9, days: 2 })\n * //=> '9 months 2 days'\n *\n * @example\n * // Customize the format\n * formatDuration(\n *   {\n *     years: 2,\n *     months: 9,\n *     weeks: 1,\n *     days: 7,\n *     hours: 5,\n *     minutes: 9,\n *     seconds: 30\n *   },\n *   { format: ['months', 'weeks'] }\n * ) === '9 months 1 week'\n *\n * @example\n * // Customize the zeros presence\n * formatDuration({ years: 0, months: 9 })\n * //=> '9 months'\n * formatDuration({ years: 0, months: 9 }, { zero: true })\n * //=> '0 years 9 months'\n *\n * @example\n * // Customize the delimiter\n * formatDuration({ years: 2, months: 9, weeks: 3 }, { delimiter: ', ' })\n * //=> '2 years, 9 months, 3 weeks'\n */\nexport function formatDuration(duration, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const format = options?.format ?? defaultFormat;\n  const zero = options?.zero ?? false;\n  const delimiter = options?.delimiter ?? \" \";\n\n  if (!locale.formatDistance) {\n    return \"\";\n  }\n\n  const result = format\n    .reduce((acc, unit) => {\n      const token = `x${unit.replace(/(^.)/, (m) => m.toUpperCase())}`;\n      const value = duration[unit];\n      if (value !== undefined && (zero || duration[unit])) {\n        return acc.concat(locale.formatDistance(token, value));\n      }\n      return acc;\n    }, [])\n    .join(delimiter);\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default formatDuration;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,yBAAyB;AACvD,SAASC,iBAAiB,QAAQ,0BAA0B;;AAE5D;AACA;AACA;;AAEA,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,CACV;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAChD,MAAMC,cAAc,GAAGL,iBAAiB,CAAC,CAAC;EAC1C,MAAMM,MAAM,GAAGF,OAAO,EAAEE,MAAM,IAAID,cAAc,CAACC,MAAM,IAAIP,aAAa;EACxE,MAAMQ,MAAM,GAAGH,OAAO,EAAEG,MAAM,IAAIN,aAAa;EAC/C,MAAMO,IAAI,GAAGJ,OAAO,EAAEI,IAAI,IAAI,KAAK;EACnC,MAAMC,SAAS,GAAGL,OAAO,EAAEK,SAAS,IAAI,GAAG;EAE3C,IAAI,CAACH,MAAM,CAACI,cAAc,EAAE;IAC1B,OAAO,EAAE;EACX;EAEA,MAAMC,MAAM,GAAGJ,MAAM,CAClBK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IACrB,MAAMC,KAAK,GAAG,IAAID,IAAI,CAACE,OAAO,CAAC,MAAM,EAAGC,CAAC,IAAKA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,EAAE;IAChE,MAAMC,KAAK,GAAGhB,QAAQ,CAACW,IAAI,CAAC;IAC5B,IAAIK,KAAK,KAAKC,SAAS,KAAKZ,IAAI,IAAIL,QAAQ,CAACW,IAAI,CAAC,CAAC,EAAE;MACnD,OAAOD,GAAG,CAACQ,MAAM,CAACf,MAAM,CAACI,cAAc,CAACK,KAAK,EAAEI,KAAK,CAAC,CAAC;IACxD;IACA,OAAON,GAAG;EACZ,CAAC,EAAE,EAAE,CAAC,CACLS,IAAI,CAACb,SAAS,CAAC;EAElB,OAAOE,MAAM;AACf;;AAEA;AACA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}