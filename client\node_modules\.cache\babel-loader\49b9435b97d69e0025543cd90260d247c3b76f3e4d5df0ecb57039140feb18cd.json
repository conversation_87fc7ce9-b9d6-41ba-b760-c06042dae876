{"ast": null, "code": "/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs//90001488-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param duration - The duration to format\n *\n * @returns The ISO 8601 duration string\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport function formatISODuration(duration) {\n  const {\n    years = 0,\n    months = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0\n  } = duration;\n  return `P${years}Y${months}M${days}DT${hours}H${minutes}M${seconds}S`;\n}\n\n// Fallback for modularized imports:\nexport default formatISODuration;", "map": {"version": 3, "names": ["formatISODuration", "duration", "years", "months", "days", "hours", "minutes", "seconds"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/formatISODuration.js"], "sourcesContent": ["/**\n * @name formatISODuration\n * @category Common Helpers\n * @summary Format a duration object according as ISO 8601 duration string\n *\n * @description\n * Format a duration object according to the ISO 8601 duration standard (https://www.digi.com/resources/documentation/digidocs//90001488-13/reference/r_iso_8601_duration_format.htm)\n *\n * @param duration - The duration to format\n *\n * @returns The ISO 8601 duration string\n *\n * @example\n * // Format the given duration as ISO 8601 string\n * const result = formatISODuration({\n *   years: 39,\n *   months: 2,\n *   days: 20,\n *   hours: 7,\n *   minutes: 5,\n *   seconds: 0\n * })\n * //=> 'P39Y2M20DT0H0M0S'\n */\nexport function formatISODuration(duration) {\n  const {\n    years = 0,\n    months = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  return `P${years}Y${months}M${days}DT${hours}H${minutes}M${seconds}S`;\n}\n\n// Fallback for modularized imports:\nexport default formatISODuration;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAACC,QAAQ,EAAE;EAC1C,MAAM;IACJC,KAAK,GAAG,CAAC;IACTC,MAAM,GAAG,CAAC;IACVC,IAAI,GAAG,CAAC;IACRC,KAAK,GAAG,CAAC;IACTC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG;EACZ,CAAC,GAAGN,QAAQ;EAEZ,OAAO,IAAIC,KAAK,IAAIC,MAAM,IAAIC,IAAI,KAAKC,KAAK,IAAIC,OAAO,IAAIC,OAAO,GAAG;AACvE;;AAEA;AACA,eAAeP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}