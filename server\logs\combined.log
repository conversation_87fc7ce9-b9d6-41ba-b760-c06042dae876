{
  message: 'Socket.IO server initialized',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: '🚀 Server running on port 3011',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: '📱 Environment: development',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: '🌐 Client URL: http://localhost:3000',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: '🔗 API URL: http://localhost:3011/api',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: '🍃 MongoDB Connected: localhost',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:21'
}
{
  message: 'SIGINT received. Shutting down gracefully...',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:19:56'
}
{
  message: 'Socket.IO server initialized',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
{
  message: '🚀 Server running on port 3011',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
{
  message: '📱 Environment: development',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
{
  message: '🌐 Client URL: http://localhost:3000',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
{
  message: '🔗 API URL: http://localhost:3011/api',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
{
  message: '🍃 MongoDB Connected: localhost',
  level: 'info',
  service: 'khurram-messenger',
  timestamp: '2025-08-05 13:20:17'
}
