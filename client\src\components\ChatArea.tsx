import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Avatar,
  Divider,
  IconButton,
} from '@mui/material';
import {
  MoreVertOutlined,
  ReplyOutlined,
} from '@mui/icons-material';

import { useChatStore, useAuthStore } from '../context/store';
import { formatMessageTime, generateInitials, generateAvatarColor } from '../utils';
import { Message } from '../types';
import MessageInput from './MessageInput';

const ChatArea: React.FC = () => {
  const { user } = useAuthStore();
  const { 
    selectedRoom, 
    selectedConversation, 
    messages, 
    sendMessage 
  } = useChatStore();
  
  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string, replyToId?: string) => {
    try {
      if (selectedRoom) {
        await sendMessage(content, selectedRoom.id);
      } else if (selectedConversation) {
        await sendMessage(content, undefined, selectedConversation.id);
      }

      // Clear reply if it was a reply
      if (replyToId) {
        setReplyToMessage(null);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleReplyToMessage = (message: Message) => {
    setReplyToMessage(message);
  };

  const handleCancelReply = () => {
    setReplyToMessage(null);
  };

  const renderMessage = (message: Message, index: number) => {
    const isOwnMessage = message.sender.id === user?.id;
    const showAvatar = index === 0 || messages[index - 1].sender.id !== message.sender.id;

    return (
      <Box
        key={message.id}
        sx={{
          display: 'flex',
          flexDirection: isOwnMessage ? 'row-reverse' : 'row',
          mb: 1,
          alignItems: 'flex-end',
        }}
      >
        {!isOwnMessage && (
          <Avatar
            sx={{
              width: 32,
              height: 32,
              mr: 1,
              bgcolor: generateAvatarColor(message.sender.username),
              visibility: showAvatar ? 'visible' : 'hidden',
            }}
            src={message.sender.avatar}
          >
            {!message.sender.avatar && generateInitials(message.sender.username)}
          </Avatar>
        )}
        
        <Box
          sx={{
            maxWidth: '70%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: isOwnMessage ? 'flex-end' : 'flex-start',
          }}
        >
          {!isOwnMessage && showAvatar && (
            <Typography variant="caption" color="text.secondary" sx={{ mb: 0.5, px: 1 }}>
              {message.sender.username}
            </Typography>
          )}
          
          <Paper
            sx={{
              p: 1.5,
              bgcolor: isOwnMessage ? 'primary.main' : 'grey.100',
              color: isOwnMessage ? 'primary.contrastText' : 'text.primary',
              borderRadius: 2,
              borderTopLeftRadius: !isOwnMessage && showAvatar ? 0 : 2,
              borderTopRightRadius: isOwnMessage && showAvatar ? 0 : 2,
              position: 'relative',
              '&:hover .message-actions': {
                opacity: 1,
              },
            }}
          >
            {/* Reply Preview */}
            {message.replyTo && (
              <Box
                sx={{
                  mb: 1,
                  p: 1,
                  bgcolor: isOwnMessage ? 'primary.dark' : 'grey.200',
                  borderRadius: 1,
                  borderLeft: 2,
                  borderColor: isOwnMessage ? 'primary.contrastText' : 'primary.main',
                }}
              >
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {message.replyTo.sender?.username || 'Unknown User'}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }} noWrap>
                  {message.replyTo.content}
                </Typography>
              </Box>
            )}

            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
              {message.content}
            </Typography>

            {/* Message Actions */}
            <Box
              className="message-actions"
              sx={{
                position: 'absolute',
                top: -20,
                right: 8,
                opacity: 0,
                transition: 'opacity 0.2s',
                bgcolor: 'background.paper',
                borderRadius: 1,
                boxShadow: 1,
                display: 'flex',
              }}
            >
              <IconButton
                size="small"
                onClick={() => handleReplyToMessage(message)}
                sx={{ p: 0.5 }}
              >
                <ReplyOutlined fontSize="small" />
              </IconButton>
            </Box>
          </Paper>
          
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ mt: 0.5, px: 1 }}
          >
            {formatMessageTime(message.createdAt)}
            {message.isEdited && ' (edited)'}
          </Typography>
        </Box>
      </Box>
    );
  };

  if (!selectedRoom && !selectedConversation) {
    return (
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          color: 'text.secondary',
        }}
      >
        <Typography variant="h6" gutterBottom>
          Welcome to Funnecto
        </Typography>
        <Typography variant="body2">
          Select a room or start a conversation to begin chatting
        </Typography>
      </Box>
    );
  }

  const chatTitle = selectedRoom ? selectedRoom.name : selectedConversation?.username;
  const chatSubtitle = selectedRoom
    ? `${selectedRoom.members?.length || 0} members`
    : selectedConversation?.status;

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              mr: 2,
              bgcolor: generateAvatarColor(chatTitle || ''),
            }}
            src={selectedRoom?.avatar || selectedConversation?.avatar}
          >
            {generateInitials(chatTitle || '')}
          </Avatar>
          <Box>
            <Typography variant="h6" noWrap>
              {chatTitle}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {chatSubtitle}
            </Typography>
          </Box>
        </Box>
        
        <IconButton>
          <MoreVertOutlined />
        </IconButton>
      </Box>

      {/* Messages Area */}
      <Box
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 2,
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {messages.map((message, index) => renderMessage(message, index))}
        
        <div ref={messagesEndRef} />
      </Box>

      <Divider />

      {/* Message Input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        placeholder={`Message ${chatTitle}...`}
        roomId={selectedRoom?.id}
        replyToMessage={replyToMessage}
        onCancelReply={handleCancelReply}
      />
    </Box>
  );
};

export default ChatArea;
