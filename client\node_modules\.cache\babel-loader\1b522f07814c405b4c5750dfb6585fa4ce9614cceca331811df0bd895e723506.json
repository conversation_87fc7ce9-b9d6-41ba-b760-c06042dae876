{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { subDays } from \"./subDays.js\";\nimport { subMonths } from \"./subMonths.js\";\n\n/**\n * The {@link sub} function options.\n */\n\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n * @param options - An object with options\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\nexport function sub(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0\n  } = duration;\n  const withoutMonths = subMonths(date, months + years * 12, options);\n  const withoutDays = subDays(withoutMonths, days + weeks * 7, options);\n  const minutesToSub = minutes + hours * 60;\n  const secondsToSub = seconds + minutesToSub * 60;\n  const msToSub = secondsToSub * 1000;\n  return constructFrom(options?.in || date, +withoutDays - msToSub);\n}\n\n// Fallback for modularized imports:\nexport default sub;", "map": {"version": 3, "names": ["constructFrom", "subDays", "subMonths", "sub", "date", "duration", "options", "years", "months", "weeks", "days", "hours", "minutes", "seconds", "withoutMonths", "withoutDays", "minutesToSub", "secondsToSub", "msToSub", "in"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/sub.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { subDays } from \"./subDays.js\";\nimport { subMonths } from \"./subMonths.js\";\n\n/**\n * The {@link sub} function options.\n */\n\n/**\n * @name sub\n * @category Common Helpers\n * @summary Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @description\n * Subtract the specified years, months, weeks, days, hours, minutes and seconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes and seconds to be subtracted\n * @param options - An object with options\n *\n * | Key     | Description                        |\n * |---------|------------------------------------|\n * | years   | Amount of years to be subtracted   |\n * | months  | Amount of months to be subtracted  |\n * | weeks   | Amount of weeks to be subtracted   |\n * | days    | Amount of days to be subtracted    |\n * | hours   | Amount of hours to be subtracted   |\n * | minutes | Amount of minutes to be subtracted |\n * | seconds | Amount of seconds to be subtracted |\n *\n * All values default to 0\n *\n * @returns The new date with the seconds subtracted\n *\n * @example\n * // Subtract the following duration from 15 June 2017 15:29:20\n * const result = sub(new Date(2017, 5, 15, 15, 29, 20), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30\n * })\n * //=> Mon Sep 1 2014 10:19:50\n */\nexport function sub(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  const withoutMonths = subMonths(date, months + years * 12, options);\n  const withoutDays = subDays(withoutMonths, days + weeks * 7, options);\n\n  const minutesToSub = minutes + hours * 60;\n  const secondsToSub = seconds + minutesToSub * 60;\n  const msToSub = secondsToSub * 1000;\n\n  return constructFrom(options?.in || date, +withoutDays - msToSub);\n}\n\n// Fallback for modularized imports:\nexport default sub;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3C,MAAM;IACJC,KAAK,GAAG,CAAC;IACTC,MAAM,GAAG,CAAC;IACVC,KAAK,GAAG,CAAC;IACTC,IAAI,GAAG,CAAC;IACRC,KAAK,GAAG,CAAC;IACTC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG;EACZ,CAAC,GAAGR,QAAQ;EAEZ,MAAMS,aAAa,GAAGZ,SAAS,CAACE,IAAI,EAAEI,MAAM,GAAGD,KAAK,GAAG,EAAE,EAAED,OAAO,CAAC;EACnE,MAAMS,WAAW,GAAGd,OAAO,CAACa,aAAa,EAAEJ,IAAI,GAAGD,KAAK,GAAG,CAAC,EAAEH,OAAO,CAAC;EAErE,MAAMU,YAAY,GAAGJ,OAAO,GAAGD,KAAK,GAAG,EAAE;EACzC,MAAMM,YAAY,GAAGJ,OAAO,GAAGG,YAAY,GAAG,EAAE;EAChD,MAAME,OAAO,GAAGD,YAAY,GAAG,IAAI;EAEnC,OAAOjB,aAAa,CAACM,OAAO,EAAEa,EAAE,IAAIf,IAAI,EAAE,CAACW,WAAW,GAAGG,OAAO,CAAC;AACnE;;AAEA;AACA,eAAef,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}