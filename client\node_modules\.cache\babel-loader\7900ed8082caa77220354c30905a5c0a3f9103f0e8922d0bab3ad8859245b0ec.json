{"ast": null, "code": "import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport GlobalStyles from \"../GlobalStyles/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\n\n/**\n * This hook returns a `GlobalStyles` component that sets the CSS layer order (for server-side rendering).\n * Then on client-side, it injects the CSS layer order into the document head to ensure that the layer order is always present first before other Emotion styles.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function useLayerOrder(theme) {\n  const upperTheme = useThemeWithoutDefault();\n  const id = useId() || '';\n  const {\n    modularCssLayers\n  } = theme;\n  let layerOrder = 'mui.global, mui.components, mui.theme, mui.custom, mui.sx';\n  if (!modularCssLayers || upperTheme !== null) {\n    // skip this hook if upper theme exists.\n    layerOrder = '';\n  } else if (typeof modularCssLayers === 'string') {\n    layerOrder = modularCssLayers.replace(/mui(?!\\.)/g, layerOrder);\n  } else {\n    layerOrder = `@layer ${layerOrder};`;\n  }\n  useEnhancedEffect(() => {\n    const head = document.querySelector('head');\n    if (!head) {\n      return;\n    }\n    const firstChild = head.firstChild;\n    if (layerOrder) {\n      // Only insert if first child doesn't have data-mui-layer-order attribute\n      if (firstChild && firstChild.hasAttribute?.('data-mui-layer-order') && firstChild.getAttribute('data-mui-layer-order') === id) {\n        return;\n      }\n      const styleElement = document.createElement('style');\n      styleElement.setAttribute('data-mui-layer-order', id);\n      styleElement.textContent = layerOrder;\n      head.prepend(styleElement);\n    } else {\n      head.querySelector(`style[data-mui-layer-order=\"${id}\"]`)?.remove();\n    }\n  }, [layerOrder, id]);\n  if (!layerOrder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GlobalStyles, {\n    styles: layerOrder\n  });\n}", "map": {"version": 3, "names": ["React", "useEnhancedEffect", "useId", "GlobalStyles", "useThemeWithoutDefault", "jsx", "_jsx", "useLayerOrder", "theme", "upperTheme", "id", "modularCssLayers", "layerOrder", "replace", "head", "document", "querySelector", "<PERSON><PERSON><PERSON><PERSON>", "hasAttribute", "getAttribute", "styleElement", "createElement", "setAttribute", "textContent", "prepend", "remove", "styles"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/ThemeProvider/useLayerOrder.js"], "sourcesContent": ["import * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport GlobalStyles from \"../GlobalStyles/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\n\n/**\n * This hook returns a `GlobalStyles` component that sets the CSS layer order (for server-side rendering).\n * Then on client-side, it injects the CSS layer order into the document head to ensure that the layer order is always present first before other Emotion styles.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function useLayerOrder(theme) {\n  const upperTheme = useThemeWithoutDefault();\n  const id = useId() || '';\n  const {\n    modularCssLayers\n  } = theme;\n  let layerOrder = 'mui.global, mui.components, mui.theme, mui.custom, mui.sx';\n  if (!modularCssLayers || upperTheme !== null) {\n    // skip this hook if upper theme exists.\n    layerOrder = '';\n  } else if (typeof modularCssLayers === 'string') {\n    layerOrder = modularCssLayers.replace(/mui(?!\\.)/g, layerOrder);\n  } else {\n    layerOrder = `@layer ${layerOrder};`;\n  }\n  useEnhancedEffect(() => {\n    const head = document.querySelector('head');\n    if (!head) {\n      return;\n    }\n    const firstChild = head.firstChild;\n    if (layerOrder) {\n      // Only insert if first child doesn't have data-mui-layer-order attribute\n      if (firstChild && firstChild.hasAttribute?.('data-mui-layer-order') && firstChild.getAttribute('data-mui-layer-order') === id) {\n        return;\n      }\n      const styleElement = document.createElement('style');\n      styleElement.setAttribute('data-mui-layer-order', id);\n      styleElement.textContent = layerOrder;\n      head.prepend(styleElement);\n    } else {\n      head.querySelector(`style[data-mui-layer-order=\"${id}\"]`)?.remove();\n    }\n  }, [layerOrder, id]);\n  if (!layerOrder) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(GlobalStyles, {\n    styles: layerOrder\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,YAAY,MAAM,0BAA0B;AACnD,OAAOC,sBAAsB,MAAM,oCAAoC;;AAEvE;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAMC,UAAU,GAAGL,sBAAsB,CAAC,CAAC;EAC3C,MAAMM,EAAE,GAAGR,KAAK,CAAC,CAAC,IAAI,EAAE;EACxB,MAAM;IACJS;EACF,CAAC,GAAGH,KAAK;EACT,IAAII,UAAU,GAAG,2DAA2D;EAC5E,IAAI,CAACD,gBAAgB,IAAIF,UAAU,KAAK,IAAI,EAAE;IAC5C;IACAG,UAAU,GAAG,EAAE;EACjB,CAAC,MAAM,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;IAC/CC,UAAU,GAAGD,gBAAgB,CAACE,OAAO,CAAC,YAAY,EAAED,UAAU,CAAC;EACjE,CAAC,MAAM;IACLA,UAAU,GAAG,UAAUA,UAAU,GAAG;EACtC;EACAX,iBAAiB,CAAC,MAAM;IACtB,MAAMa,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3C,IAAI,CAACF,IAAI,EAAE;MACT;IACF;IACA,MAAMG,UAAU,GAAGH,IAAI,CAACG,UAAU;IAClC,IAAIL,UAAU,EAAE;MACd;MACA,IAAIK,UAAU,IAAIA,UAAU,CAACC,YAAY,GAAG,sBAAsB,CAAC,IAAID,UAAU,CAACE,YAAY,CAAC,sBAAsB,CAAC,KAAKT,EAAE,EAAE;QAC7H;MACF;MACA,MAAMU,YAAY,GAAGL,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;MACpDD,YAAY,CAACE,YAAY,CAAC,sBAAsB,EAAEZ,EAAE,CAAC;MACrDU,YAAY,CAACG,WAAW,GAAGX,UAAU;MACrCE,IAAI,CAACU,OAAO,CAACJ,YAAY,CAAC;IAC5B,CAAC,MAAM;MACLN,IAAI,CAACE,aAAa,CAAC,+BAA+BN,EAAE,IAAI,CAAC,EAAEe,MAAM,CAAC,CAAC;IACrE;EACF,CAAC,EAAE,CAACb,UAAU,EAAEF,EAAE,CAAC,CAAC;EACpB,IAAI,CAACE,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,OAAO,aAAaN,IAAI,CAACH,YAAY,EAAE;IACrCuB,MAAM,EAAEd;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}