const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens']
  },
  email: {
    type: String,
    required: false, // Made optional for LAN users
    unique: true,
    sparse: true, // Allow multiple null values
    lowercase: true,
    trim: true,
    validate: {
      validator: function(email) {
        // Only validate email format if email is provided
        return !email || /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(email);
      },
      message: 'Please enter a valid email'
    }
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long'],
    select: false // Don't include password in queries by default
  },
  userType: {
    type: String,
    enum: ['lan', 'wan', 'developer'],
    default: function() {
      return this.email ? 'wan' : 'lan';
    }
  },
  isDeveloper: {
    type: Boolean,
    default: false,
    select: false
  },
  userType: {
    type: String,
    enum: ['lan', 'wan', 'developer'],
    default: function() {
      return this.email ? 'wan' : 'lan';
    }
  },
  isDeveloper: {
    type: Boolean,
    default: false,
    select: false
  },
  avatar: {
    type: String,
    default: null
  },
  status: {
    type: String,
    enum: ['online', 'away', 'busy', 'offline'],
    default: 'offline'
  },
  lastSeen: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  socketId: {
    type: String,
    default: null
  },
  rooms: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room'
  }],
  blockedUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    notifications: {
      type: Boolean,
      default: true
    },
    soundEnabled: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index for better performance (username and email already have unique indexes)
userSchema.index({ status: 1 });

// Virtual for user's full profile
userSchema.virtual('profile').get(function() {
  return {
    id: this._id,
    username: this.username,
    email: this.email,
    avatar: this.avatar,
    status: this.status,
    lastSeen: this.lastSeen,
    preferences: this.preferences
  };
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Update lastSeen on save
userSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status !== 'offline') {
    this.lastSeen = new Date();
  }
  next();
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Generate JWT token
userSchema.methods.generateAuthToken = function() {
  return jwt.sign(
    { 
      id: this._id, 
      username: this.username,
      email: this.email 
    },
    process.env.JWT_SECRET || 'fallback_secret_key',
    { 
      expiresIn: process.env.JWT_EXPIRE || '7d' 
    }
  );
};

// Update user status
userSchema.methods.updateStatus = async function(status, socketId = null) {
  this.status = status;
  this.lastSeen = new Date();
  if (socketId) this.socketId = socketId;
  return await this.save();
};

// Check if user is online
userSchema.methods.isOnline = function() {
  return this.status === 'online' && this.socketId;
};

// Get safe user data (without sensitive info)
userSchema.methods.getSafeData = function() {
  return {
    id: this._id,
    username: this.username,
    avatar: this.avatar,
    status: this.status,
    lastSeen: this.lastSeen
  };
};

// Static method to create developer user
userSchema.statics.createDeveloperUser = async function() {
  const developerExists = await this.findOne({ isDeveloper: true });
  if (!developerExists) {
    const developer = new this({
      username: 'funnecto_dev',
      password: 'Dev@2024!Secure',
      userType: 'developer',
      isDeveloper: true,
      status: 'offline',
      isActive: true
    });
    await developer.save();
    console.log('Developer user created successfully');
  }
};

module.exports = mongoose.model('User', userSchema);
