// User types
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: string;
  preferences: {
    theme: 'light' | 'dark';
    notifications: boolean;
    soundEnabled: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UserRegistration {
  username: string;
  email?: string;
  password: string;
}

export interface UserLogin {
  emailOrUsername: string;
  password: string;
}

export interface UserUpdate {
  username?: string;
  email?: string;
  avatar?: string;
  preferences?: Partial<User['preferences']>;
}

// Message types
export interface Message {
  id: string;
  content: string;
  sender: User;
  recipient?: User;
  room?: Room;
  type: 'text' | 'image' | 'file' | 'system';
  messageStatus: 'sent' | 'delivered' | 'read';
  isEdited: boolean;
  editedAt?: string;
  isDeleted: boolean;
  deletedAt?: string;
  replyTo?: Message;
  reactions: Reaction[];
  attachments: Attachment[];
  formatting?: MessageFormatting;
  readBy: ReadReceipt[];
  createdAt: string;
  updatedAt: string;
}

export interface MessageSend {
  content: string;
  recipient?: string;
  room?: string;
  type?: 'text' | 'image' | 'file';
  replyTo?: string;
}

export interface Reaction {
  user: User;
  emoji: string;
  createdAt: string;
}

export interface Attachment {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
}

export interface MessageFormatting {
  bold: Array<{ start: number; end: number }>;
  italic: Array<{ start: number; end: number }>;
  code: Array<{ start: number; end: number }>;
  link: Array<{ start: number; end: number; url: string }>;
}

export interface ReadReceipt {
  user: User;
  readAt: string;
}

// Room types
export interface Room {
  id: string;
  name: string;
  description: string;
  type: 'public' | 'private' | 'direct';
  creator: User;
  members: RoomMember[];
  settings: RoomSettings;
  avatar?: string;
  lastActivity: string;
  messageCount: number;
  isActive: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface RoomMember {
  user: User;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: string;
  lastSeen: string;
  isActive: boolean;
}

export interface RoomSettings {
  isPublic: boolean;
  allowInvites: boolean;
  maxMembers: number;
  messageRetention: number;
}

export interface RoomCreate {
  name: string;
  description?: string;
  type?: 'public' | 'private';
  settings?: Partial<RoomSettings>;
}

export interface RoomUpdate {
  name?: string;
  description?: string;
  settings?: Partial<RoomSettings>;
}

// Socket types
export interface SocketEvents {
  // Connection events
  connect: () => void;
  disconnect: () => void;
  
  // User events
  userStatusUpdate: (data: { userId: string; username: string; status: string }) => void;
  userTyping: (data: { userId: string; username: string; isTyping: boolean }) => void;
  
  // Message events
  newMessage: (message: Message) => void;
  messageReaction: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => void;
  
  // Room events
  userRooms: (rooms: Room[]) => void;
  roomMessages: (data: { roomId: string; messages: Message[] }) => void;
  onlineUsers: (users: User[]) => void;
  
  // Error events
  error: (data: { message: string }) => void;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    details?: any;
  };
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface PaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasMore?: boolean;
  };
}

// UI State types
export interface ChatState {
  currentUser: User | null;
  selectedRoom: Room | null;
  selectedConversation: User | null;
  messages: Message[];
  rooms: Room[];
  onlineUsers: User[];
  isLoading: boolean;
  error: string | null;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Theme types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  surfaceColor: string;
  textColor: string;
  borderColor: string;
}

// Notification types
export interface NotificationData {
  id: string;
  type: 'message' | 'mention' | 'system';
  title: string;
  body: string;
  icon?: string;
  data?: any;
  timestamp: string;
}

// Form types
export interface FormErrors {
  [key: string]: string | undefined;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | undefined;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
}

// Constants
export const USER_STATUS = {
  ONLINE: 'online',
  AWAY: 'away',
  BUSY: 'busy',
  OFFLINE: 'offline'
} as const;

export const MESSAGE_TYPE = {
  TEXT: 'text',
  IMAGE: 'image',
  FILE: 'file',
  SYSTEM: 'system'
} as const;

export const ROOM_TYPE = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  DIRECT: 'direct'
} as const;

export const THEME_MODE = {
  LIGHT: 'light',
  DARK: 'dark'
} as const;
