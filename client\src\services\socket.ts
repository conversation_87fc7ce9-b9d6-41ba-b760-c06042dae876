import { io, Socket } from 'socket.io-client';
import { Message, Room, User, SocketEvents } from '../types';

class SocketService {
  private socket: Socket | null = null;
  private serverURL: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.serverURL = process.env.REACT_APP_SERVER_URL || 'http://localhost:3011';
  }

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.socket = io(this.serverURL, {
        auth: {
          token
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log('Connected to server');
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('Disconnected from server:', reason);
        
        if (reason === 'io server disconnect') {
          // Server disconnected the client, try to reconnect
          this.handleReconnect();
        }
      });

      this.socket.on('error', (error) => {
        console.error('Socket error:', error);
      });

      // Set up reconnection logic
      this.socket.on('reconnect', (attemptNumber) => {
        console.log('Reconnected after', attemptNumber, 'attempts');
        this.reconnectAttempts = 0;
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('Reconnection error:', error);
        this.handleReconnect();
      });
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        if (this.socket && !this.socket.connected) {
          this.socket.connect();
        }
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Room management
  joinRoom(roomId: string): void {
    if (this.socket) {
      this.socket.emit('joinRoom', roomId);
    }
  }

  leaveRoom(roomId: string): void {
    if (this.socket) {
      this.socket.emit('leaveRoom', roomId);
    }
  }

  // Message handling
  sendMessage(data: {
    content: string;
    roomId?: string;
    recipientId?: string;
    type?: string;
    replyTo?: string;
  }): void {
    if (this.socket) {
      this.socket.emit('sendMessage', data);
    }
  }

  // Typing indicators
  sendTyping(roomId: string, isTyping: boolean): void {
    if (this.socket) {
      this.socket.emit('typing', { roomId, isTyping });
    }
  }

  // Reactions
  addReaction(messageId: string, emoji: string): void {
    if (this.socket) {
      this.socket.emit('addReaction', { messageId, emoji });
    }
  }

  removeReaction(messageId: string): void {
    if (this.socket) {
      this.socket.emit('removeReaction', { messageId });
    }
  }

  // Event listeners
  onNewMessage(callback: (message: Message) => void): void {
    if (this.socket) {
      this.socket.on('newMessage', callback);
    }
  }

  onUserStatusUpdate(callback: (data: { userId: string; username: string; status: string }) => void): void {
    if (this.socket) {
      this.socket.on('userStatusUpdate', callback);
    }
  }

  onUserTyping(callback: (data: { userId: string; username: string; isTyping: boolean }) => void): void {
    if (this.socket) {
      this.socket.on('userTyping', callback);
    }
  }

  onMessageReaction(callback: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => void): void {
    if (this.socket) {
      this.socket.on('messageReaction', callback);
    }
  }

  onUserRooms(callback: (rooms: Room[]) => void): void {
    if (this.socket) {
      this.socket.on('userRooms', callback);
    }
  }

  onRoomMessages(callback: (data: { roomId: string; messages: Message[] }) => void): void {
    if (this.socket) {
      this.socket.on('roomMessages', callback);
    }
  }

  onOnlineUsers(callback: (users: User[]) => void): void {
    if (this.socket) {
      this.socket.on('onlineUsers', callback);
    }
  }

  onError(callback: (data: { message: string }) => void): void {
    if (this.socket) {
      this.socket.on('error', callback);
    }
  }

  // Remove event listeners
  offNewMessage(callback?: (message: Message) => void): void {
    if (this.socket) {
      this.socket.off('newMessage', callback);
    }
  }

  offUserStatusUpdate(callback?: (data: { userId: string; username: string; status: string }) => void): void {
    if (this.socket) {
      this.socket.off('userStatusUpdate', callback);
    }
  }

  offUserTyping(callback?: (data: { userId: string; username: string; isTyping: boolean }) => void): void {
    if (this.socket) {
      this.socket.off('userTyping', callback);
    }
  }

  offMessageReaction(callback?: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => void): void {
    if (this.socket) {
      this.socket.off('messageReaction', callback);
    }
  }

  offUserRooms(callback?: (rooms: Room[]) => void): void {
    if (this.socket) {
      this.socket.off('userRooms', callback);
    }
  }

  offRoomMessages(callback?: (data: { roomId: string; messages: Message[] }) => void): void {
    if (this.socket) {
      this.socket.off('roomMessages', callback);
    }
  }

  offOnlineUsers(callback?: (users: User[]) => void): void {
    if (this.socket) {
      this.socket.off('onlineUsers', callback);
    }
  }

  offError(callback?: (data: { message: string }) => void): void {
    if (this.socket) {
      this.socket.off('error', callback);
    }
  }

  // Remove all listeners
  removeAllListeners(): void {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  // Get socket instance (for advanced usage)
  getSocket(): Socket | null {
    return this.socket;
  }
}

export const socketService = new SocketService();
export default socketService;
