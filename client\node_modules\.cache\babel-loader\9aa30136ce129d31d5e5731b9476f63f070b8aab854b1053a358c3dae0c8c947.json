{"ast": null, "code": "import { addDays } from \"./addDays.js\";\nimport { addMonths } from \"./addMonths.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link add} function options.\n */\n\n/**\n * @name add\n * @category Common Helpers\n * @summary Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @description\n * Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @typeParam DateType - The `Date` type the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes, and seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add the following duration to 1 September 2014, 10:19:50\n * const result = add(new Date(2014, 8, 1, 10, 19, 50), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30,\n * })\n * //=> Thu Jun 15 2017 15:29:20\n */\nexport function add(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0\n  } = duration;\n\n  // Add years and months\n  const _date = toDate(date, options?.in);\n  const dateWithMonths = months || years ? addMonths(_date, months + years * 12) : _date;\n\n  // Add weeks and days\n  const dateWithDays = days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;\n\n  // Add days, hours, minutes, and seconds\n  const minutesToAdd = minutes + hours * 60;\n  const secondsToAdd = seconds + minutesToAdd * 60;\n  const msToAdd = secondsToAdd * 1000;\n  return constructFrom(options?.in || date, +dateWithDays + msToAdd);\n}\n\n// Fallback for modularized imports:\nexport default add;", "map": {"version": 3, "names": ["addDays", "addMonths", "constructFrom", "toDate", "add", "date", "duration", "options", "years", "months", "weeks", "days", "hours", "minutes", "seconds", "_date", "in", "dateWithMonths", "dateWithDays", "minutesToAdd", "secondsToAdd", "msToAdd"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/add.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\nimport { addMonths } from \"./addMonths.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link add} function options.\n */\n\n/**\n * @name add\n * @category Common Helpers\n * @summary Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @description\n * Add the specified years, months, weeks, days, hours, minutes, and seconds to the given date.\n *\n * @typeParam DateType - The `Date` type the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param duration - The object with years, months, weeks, days, hours, minutes, and seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add the following duration to 1 September 2014, 10:19:50\n * const result = add(new Date(2014, 8, 1, 10, 19, 50), {\n *   years: 2,\n *   months: 9,\n *   weeks: 1,\n *   days: 7,\n *   hours: 5,\n *   minutes: 9,\n *   seconds: 30,\n * })\n * //=> Thu Jun 15 2017 15:29:20\n */\nexport function add(date, duration, options) {\n  const {\n    years = 0,\n    months = 0,\n    weeks = 0,\n    days = 0,\n    hours = 0,\n    minutes = 0,\n    seconds = 0,\n  } = duration;\n\n  // Add years and months\n  const _date = toDate(date, options?.in);\n  const dateWithMonths =\n    months || years ? addMonths(_date, months + years * 12) : _date;\n\n  // Add weeks and days\n  const dateWithDays =\n    days || weeks ? addDays(dateWithMonths, days + weeks * 7) : dateWithMonths;\n\n  // Add days, hours, minutes, and seconds\n  const minutesToAdd = minutes + hours * 60;\n  const secondsToAdd = seconds + minutesToAdd * 60;\n  const msToAdd = secondsToAdd * 1000;\n\n  return constructFrom(options?.in || date, +dateWithDays + msToAdd);\n}\n\n// Fallback for modularized imports:\nexport default add;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,GAAGA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC3C,MAAM;IACJC,KAAK,GAAG,CAAC;IACTC,MAAM,GAAG,CAAC;IACVC,KAAK,GAAG,CAAC;IACTC,IAAI,GAAG,CAAC;IACRC,KAAK,GAAG,CAAC;IACTC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG;EACZ,CAAC,GAAGR,QAAQ;;EAEZ;EACA,MAAMS,KAAK,GAAGZ,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAES,EAAE,CAAC;EACvC,MAAMC,cAAc,GAClBR,MAAM,IAAID,KAAK,GAAGP,SAAS,CAACc,KAAK,EAAEN,MAAM,GAAGD,KAAK,GAAG,EAAE,CAAC,GAAGO,KAAK;;EAEjE;EACA,MAAMG,YAAY,GAChBP,IAAI,IAAID,KAAK,GAAGV,OAAO,CAACiB,cAAc,EAAEN,IAAI,GAAGD,KAAK,GAAG,CAAC,CAAC,GAAGO,cAAc;;EAE5E;EACA,MAAME,YAAY,GAAGN,OAAO,GAAGD,KAAK,GAAG,EAAE;EACzC,MAAMQ,YAAY,GAAGN,OAAO,GAAGK,YAAY,GAAG,EAAE;EAChD,MAAME,OAAO,GAAGD,YAAY,GAAG,IAAI;EAEnC,OAAOlB,aAAa,CAACK,OAAO,EAAES,EAAE,IAAIX,IAAI,EAAE,CAACa,YAAY,GAAGG,OAAO,CAAC;AACpE;;AAEA;AACA,eAAejB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}