{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider({\n  value,\n  ...props\n}) {\n  return /*#__PURE__*/_jsx(RtlContext.Provider, {\n    value: value ?? true,\n    ...props\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value ?? false;\n};\nexport default RtlProvider;", "map": {"version": 3, "names": ["React", "PropTypes", "jsx", "_jsx", "RtlContext", "createContext", "RtlProvider", "value", "props", "Provider", "process", "env", "NODE_ENV", "propTypes", "children", "node", "bool", "useRtl", "useContext"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/RtlProvider/index.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RtlContext = /*#__PURE__*/React.createContext();\nfunction RtlProvider({\n  value,\n  ...props\n}) {\n  return /*#__PURE__*/_jsx(RtlContext.Provider, {\n    value: value ?? true,\n    ...props\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RtlProvider.propTypes = {\n  children: PropTypes.node,\n  value: PropTypes.bool\n} : void 0;\nexport const useRtl = () => {\n  const value = React.useContext(RtlContext);\n  return value ?? false;\n};\nexport default RtlProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC,CAAC;AACrD,SAASC,WAAWA,CAAC;EACnBC,KAAK;EACL,GAAGC;AACL,CAAC,EAAE;EACD,OAAO,aAAaL,IAAI,CAACC,UAAU,CAACK,QAAQ,EAAE;IAC5CF,KAAK,EAAEA,KAAK,IAAI,IAAI;IACpB,GAAGC;EACL,CAAC,CAAC;AACJ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGN,WAAW,CAACO,SAAS,GAAG;EAC9DC,QAAQ,EAAEb,SAAS,CAACc,IAAI;EACxBR,KAAK,EAAEN,SAAS,CAACe;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,OAAO,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAC1B,MAAMV,KAAK,GAAGP,KAAK,CAACkB,UAAU,CAACd,UAAU,CAAC;EAC1C,OAAOG,KAAK,IAAI,KAAK;AACvB,CAAC;AACD,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}