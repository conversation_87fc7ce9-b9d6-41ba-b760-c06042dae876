import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  Tabs,
  Tab,
  Badge,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  GroupOutlined,
  PersonOutlined,
  AddOutlined,
  SearchOutlined,
} from '@mui/icons-material';

import { useChatStore } from '../context/store';
import { generateInitials, generateAvatarColor, formatLastSeen } from '../utils';
import { Room, User } from '../types';
import CreateRoomDialog from './CreateRoomDialog';
import UserSearchDialog from './UserSearchDialog';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`sidebar-tabpanel-${index}`}
      aria-labelledby={`sidebar-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const Sidebar: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [createRoomOpen, setCreateRoomOpen] = useState(false);
  const [userSearchOpen, setUserSearchOpen] = useState(false);
  const {
    rooms,
    selectedRoom,
    selectedConversation,
    setSelectedRoom,
    setSelectedConversation,
    onlineUsers
  } = useChatStore();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleRoomSelect = (room: Room) => {
    setSelectedRoom(room);
  };

  const handleUserSelect = (user: User) => {
    setSelectedConversation(user);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          aria-label="sidebar tabs"
        >
          <Tab
            icon={<GroupOutlined />}
            label="Rooms"
            id="sidebar-tab-0"
            aria-controls="sidebar-tabpanel-0"
          />
          <Tab
            icon={<PersonOutlined />}
            label="Chats"
            id="sidebar-tab-1"
            aria-controls="sidebar-tabpanel-1"
          />
        </Tabs>
      </Box>

      {/* Tab Content */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {/* Rooms Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ p: 1, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="subtitle2" color="text.secondary">
                Rooms ({rooms.length})
              </Typography>
              <Box>
                <Tooltip title="Search Rooms">
                  <IconButton size="small">
                    <SearchOutlined fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Create Room">
                  <IconButton size="small" onClick={() => setCreateRoomOpen(true)}>
                    <AddOutlined fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
          </Box>
          
          <List sx={{ py: 0, overflow: 'auto', height: 'calc(100% - 60px)' }}>
            {rooms.map((room) => (
              <ListItem key={room.id} disablePadding>
                <ListItemButton
                  selected={selectedRoom?.id === room.id}
                  onClick={() => handleRoomSelect(room)}
                  sx={{
                    '&.Mui-selected': {
                      bgcolor: 'primary.light',
                      color: 'primary.contrastText',
                      '&:hover': {
                        bgcolor: 'primary.main',
                      },
                    },
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor: generateAvatarColor(room.name),
                        width: 40,
                        height: 40,
                      }}
                      src={room.avatar}
                    >
                      {!room.avatar && generateInitials(room.name)}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2" noWrap>
                        {room.name}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary" noWrap>
                        {room.members?.length || 0} members • {formatLastSeen(room.lastActivity)}
                      </Typography>
                    }
                  />
                  {room.messageCount > 0 && (
                    <Badge
                      badgeContent={room.messageCount}
                      color="primary"
                      max={99}
                    />
                  )}
                </ListItemButton>
              </ListItem>
            ))}
            
            {rooms.length === 0 && (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No rooms available
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Create or join a room to start chatting
                </Typography>
              </Box>
            )}
          </List>
        </TabPanel>

        {/* Direct Messages Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ p: 1, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="subtitle2" color="text.secondary">
                Direct Messages
              </Typography>
              <Tooltip title="Search Users">
                <IconButton size="small" onClick={() => setUserSearchOpen(true)}>
                  <SearchOutlined fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          
          <List sx={{ py: 0, overflow: 'auto', height: 'calc(100% - 60px)' }}>
            {onlineUsers.map((user) => (
              <ListItem key={user.id} disablePadding>
                <ListItemButton
                  selected={selectedConversation?.id === user.id}
                  onClick={() => handleUserSelect(user)}
                  sx={{
                    '&.Mui-selected': {
                      bgcolor: 'primary.light',
                      color: 'primary.contrastText',
                      '&:hover': {
                        bgcolor: 'primary.main',
                      },
                    },
                  }}
                >
                  <ListItemAvatar>
                    <Badge
                      overlap="circular"
                      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                      variant="dot"
                      sx={{
                        '& .MuiBadge-badge': {
                          backgroundColor: user.status === 'online' ? '#44b700' : '#grey.500',
                          color: user.status === 'online' ? '#44b700' : '#grey.500',
                          '&::after': {
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width: '100%',
                            height: '100%',
                            borderRadius: '50%',
                            animation: user.status === 'online' ? 'ripple 1.2s infinite ease-in-out' : 'none',
                            border: '1px solid currentColor',
                            content: '""',
                          },
                        },
                        '@keyframes ripple': {
                          '0%': {
                            transform: 'scale(.8)',
                            opacity: 1,
                          },
                          '100%': {
                            transform: 'scale(2.4)',
                            opacity: 0,
                          },
                        },
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: generateAvatarColor(user.username),
                          width: 40,
                          height: 40,
                        }}
                        src={user.avatar}
                      >
                        {!user.avatar && generateInitials(user.username)}
                      </Avatar>
                    </Badge>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle2" noWrap>
                        {user.username}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary" noWrap>
                        {user.status === 'online' ? 'Online' : formatLastSeen(user.lastSeen)}
                      </Typography>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
            
            {onlineUsers.length === 0 && (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No users online
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Users will appear here when they come online
                </Typography>
              </Box>
            )}
          </List>
        </TabPanel>
      </Box>

      {/* Create Room Dialog */}
      <CreateRoomDialog
        open={createRoomOpen}
        onClose={() => setCreateRoomOpen(false)}
      />

      {/* User Search Dialog */}
      <UserSearchDialog
        open={userSearchOpen}
        onClose={() => setUserSearchOpen(false)}
      />
    </Box>
  );
};

export default Sidebar;
