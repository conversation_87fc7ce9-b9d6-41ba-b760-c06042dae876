{"ast": null, "code": "import { addISOWeekYears } from \"./addISOWeekYears.js\";\n\n/**\n * The {@link subISOWeekYears} function options.\n */\n\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the ISO week-numbering years subtracted\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\nexport function subISOWeekYears(date, amount, options) {\n  return addISOWeekYears(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subISOWeekYears;", "map": {"version": 3, "names": ["addISOWeekYears", "subISOWeekYears", "date", "amount", "options"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/subISOWeekYears.js"], "sourcesContent": ["import { addISOWeekYears } from \"./addISOWeekYears.js\";\n\n/**\n * The {@link subISOWeekYears} function options.\n */\n\n/**\n * @name subISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Subtract the specified number of ISO week-numbering years from the given date.\n *\n * @description\n * Subtract the specified number of ISO week-numbering years from the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be subtracted.\n * @param options - The options\n *\n * @returns The new date with the ISO week-numbering years subtracted\n *\n * @example\n * // Subtract 5 ISO week-numbering years from 1 September 2014:\n * const result = subISOWeekYears(new Date(2014, 8, 1), 5)\n * //=> Mon Aug 31 2009 00:00:00\n */\nexport function subISOWeekYears(date, amount, options) {\n  return addISOWeekYears(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subISOWeekYears;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,OAAOJ,eAAe,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAChD;;AAEA;AACA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}