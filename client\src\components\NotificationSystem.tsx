import React, { useEffect, useState } from 'react';
import {
  Box,
  IconButton,
  Badge,
  Menu,
  MenuItem,
  Typography,
  Avatar,
  Divider,
  Button,
} from '@mui/material';
import {
  NotificationsOutlined,
  NotificationsOffOutlined,
  MarkEmailReadOutlined,
  MessageOutlined,
  GroupOutlined,
} from '@mui/icons-material';

import { useAuthStore } from '../context/store';
import { useSocketEvents } from '../hooks';
import { formatMessageTime, generateInitials, generateAvatarColor, requestNotificationPermission, showNotification } from '../utils';
import { Message, User } from '../types';

interface NotificationItem {
  id: string;
  type: 'message' | 'mention' | 'room_invite';
  title: string;
  body: string;
  avatar?: string;
  timestamp: string;
  read: boolean;
  data?: any;
}

const NotificationSystem: React.FC = () => {
  const { user } = useAuthStore();
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const socketEvents = useSocketEvents();

  // Request notification permission on mount
  useEffect(() => {
    requestNotificationPermission().then((permission) => {
      setNotificationsEnabled(permission === 'granted');
    });
  }, []);

  // Set up socket event listeners for notifications
  useEffect(() => {
    const unsubscribers = [
      socketEvents.onNewMessage((message: Message) => {
        // Only show notification if message is not from current user
        if (message.sender.id !== user?.id) {
          const notification: NotificationItem = {
            id: message.id,
            type: 'message',
            title: message.room ? `${message.room.name}` : `${message.sender.username}`,
            body: message.content,
            avatar: message.sender.avatar,
            timestamp: message.createdAt,
            read: false,
            data: { message }
          };

          setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep only 50 notifications

          // Show browser notification if enabled
          if (notificationsEnabled && document.hidden) {
            showNotification(notification.title, {
              body: notification.body,
              icon: notification.avatar || '/Funnectologo.png',
              tag: `message-${message.id}`,
            });
          }
        }
      }),

      socketEvents.onUserStatusUpdate((data: { userId: string; username: string; status: string }) => {
        if (data.status === 'online' && data.userId !== user?.id) {
          const notification: NotificationItem = {
            id: `status-${data.userId}-${Date.now()}`,
            type: 'message',
            title: 'User Online',
            body: `${data.username} is now online`,
            timestamp: new Date().toISOString(),
            read: false,
            data: { userId: data.userId }
          };

          setNotifications(prev => [notification, ...prev.slice(0, 49)]);
        }
      }),
    ];

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [socketEvents, user?.id, notificationsEnabled]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMarkAllRead = () => {
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })));
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const handleClearAll = () => {
    setNotifications([]);
  };

  const handleNotificationClick = (notification: NotificationItem) => {
    handleMarkAsRead(notification.id);
    handleClose();
    
    // Handle navigation based on notification type
    if (notification.type === 'message' && notification.data?.message) {
      // TODO: Navigate to the message/room
      console.log('Navigate to message:', notification.data.message);
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;
  const open = Boolean(anchorEl);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message':
        return <MessageOutlined fontSize="small" />;
      case 'room_invite':
        return <GroupOutlined fontSize="small" />;
      default:
        return <NotificationsOutlined fontSize="small" />;
    }
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        aria-label="notifications"
        aria-controls={open ? 'notification-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <Badge badgeContent={unreadCount} color="error" max={99}>
          {notificationsEnabled ? (
            <NotificationsOutlined />
          ) : (
            <NotificationsOffOutlined />
          )}
        </Badge>
      </IconButton>

      <Menu
        id="notification-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 360,
            maxHeight: 480,
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              Notifications
            </Typography>
            <Box>
              {unreadCount > 0 && (
                <IconButton size="small" onClick={handleMarkAllRead} title="Mark all as read">
                  <MarkEmailReadOutlined fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>
          {unreadCount > 0 && (
            <Typography variant="caption" color="text.secondary">
              {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
            </Typography>
          )}
        </Box>

        {/* Notifications List */}
        <Box sx={{ maxHeight: 320, overflow: 'auto' }}>
          {notifications.length === 0 ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <NotificationsOutlined sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                No notifications yet
              </Typography>
            </Box>
          ) : (
            notifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <MenuItem
                  onClick={() => handleNotificationClick(notification)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    bgcolor: notification.read ? 'transparent' : 'action.hover',
                    '&:hover': {
                      bgcolor: 'action.selected',
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%', gap: 1.5 }}>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: generateAvatarColor(notification.title),
                      }}
                      src={notification.avatar}
                    >
                      {!notification.avatar && generateInitials(notification.title)}
                    </Avatar>
                    
                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getNotificationIcon(notification.type)}
                        <Typography variant="subtitle2" noWrap>
                          {notification.title}
                        </Typography>
                        {!notification.read && (
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: 'primary.main',
                            }}
                          />
                        )}
                      </Box>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {notification.body}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatMessageTime(notification.timestamp)}
                      </Typography>
                    </Box>
                  </Box>
                </MenuItem>
                {index < notifications.length - 1 && <Divider />}
              </React.Fragment>
            ))
          )}
        </Box>

        {/* Footer */}
        {notifications.length > 0 && (
          <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
            <Button
              fullWidth
              size="small"
              onClick={handleClearAll}
              color="inherit"
            >
              Clear All
            </Button>
          </Box>
        )}
      </Menu>
    </>
  );
};

export default NotificationSystem;
