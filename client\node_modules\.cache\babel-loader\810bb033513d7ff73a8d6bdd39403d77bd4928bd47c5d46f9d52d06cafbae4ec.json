{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInMonth} function options.\n */\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date, considering the context if provided.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(_date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;", "map": {"version": 3, "names": ["constructFrom", "toDate", "getDaysInMonth", "date", "options", "_date", "in", "year", "getFullYear", "monthIndex", "getMonth", "lastDayOfMonth", "setFullYear", "setHours", "getDate"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/getDaysInMonth.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInMonth} function options.\n */\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date, considering the context if provided.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(_date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,MAAMC,UAAU,GAAGJ,KAAK,CAACK,QAAQ,CAAC,CAAC;EACnC,MAAMC,cAAc,GAAGX,aAAa,CAACK,KAAK,EAAE,CAAC,CAAC;EAC9CM,cAAc,CAACC,WAAW,CAACL,IAAI,EAAEE,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;EACnDE,cAAc,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnC,OAAOF,cAAc,CAACG,OAAO,CAAC,CAAC;AACjC;;AAEA;AACA,eAAeZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}