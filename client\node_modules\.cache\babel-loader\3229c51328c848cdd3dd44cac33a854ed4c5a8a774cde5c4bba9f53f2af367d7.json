{"ast": null, "code": "import { addDays } from \"./addDays.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isTomorrow} function options.\n */\n\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is tomorrow\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport function isTomorrow(date, options) {\n  return isSameDay(date, addDays(constructNow(options?.in || date), 1), options);\n}\n\n// Fallback for modularized imports:\nexport default isTomorrow;", "map": {"version": 3, "names": ["addDays", "constructNow", "isSameDay", "isTomorrow", "date", "options", "in"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/isTomorrow.js"], "sourcesContent": ["import { addDays } from \"./addDays.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameDay } from \"./isSameDay.js\";\n\n/**\n * The {@link isTomorrow} function options.\n */\n\n/**\n * @name isTomorrow\n * @category Day Helpers\n * @summary Is the given date tomorrow?\n * @pure false\n *\n * @description\n * Is the given date tomorrow?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is tomorrow\n *\n * @example\n * // If today is 6 October 2014, is 7 October 14:00:00 tomorrow?\n * const result = isTomorrow(new Date(2014, 9, 7, 14, 0))\n * //=> true\n */\nexport function isTomorrow(date, options) {\n  return isSameDay(\n    date,\n    addDays(constructNow(options?.in || date), 1),\n    options,\n  );\n}\n\n// Fallback for modularized imports:\nexport default isTomorrow;\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,SAAS,CACdE,IAAI,EACJJ,OAAO,CAACC,YAAY,CAACI,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAAC,EAAE,CAAC,CAAC,EAC7CC,OACF,CAAC;AACH;;AAEA;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}