{"ast": null, "code": "import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), Den<PERSON>, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n  doPoll() {\n    this._fetch().then(res => {\n      if (!res.ok) {\n        return this.onError(\"fetch read error\", res.status, res);\n      }\n      res.text().then(data => this.onData(data));\n    }).catch(err => {\n      this.onError(\"fetch read error\", err);\n    });\n  }\n  doWrite(data, callback) {\n    this._fetch(data).then(res => {\n      if (!res.ok) {\n        return this.onError(\"fetch write error\", res.status, res);\n      }\n      callback();\n    }).catch(err => {\n      this.onError(\"fetch write error\", err);\n    });\n  }\n  _fetch(data) {\n    var _a;\n    const isPost = data !== undefined;\n    const headers = new Headers(this.opts.extraHeaders);\n    if (isPost) {\n      headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n    }\n    (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n    return fetch(this.uri(), {\n      method: isPost ? \"POST\" : \"GET\",\n      body: isPost ? data : null,\n      headers,\n      credentials: this.opts.withCredentials ? \"include\" : \"omit\"\n    }).then(res => {\n      var _a;\n      // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n      (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n      return res;\n    });\n  }\n}", "map": {"version": 3, "names": ["Polling", "<PERSON>tch", "doPoll", "_fetch", "then", "res", "ok", "onError", "status", "text", "data", "onData", "catch", "err", "doWrite", "callback", "_a", "isPost", "undefined", "headers", "Headers", "opts", "extraHeaders", "set", "socket", "_cookieJar", "appendCookies", "fetch", "uri", "method", "body", "credentials", "withCredentials", "parseCookies", "getSetCookie"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/engine.io-client/build/esm/transports/polling-fetch.js"], "sourcesContent": ["import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,KAAK,SAASD,OAAO,CAAC;EAC/BE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACC,MAAM,CAAC,CAAC,CACRC,IAAI,CAAEC,GAAG,IAAK;MACf,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE;QACT,OAAO,IAAI,CAACC,OAAO,CAAC,kBAAkB,EAAEF,GAAG,CAACG,MAAM,EAAEH,GAAG,CAAC;MAC5D;MACAA,GAAG,CAACI,IAAI,CAAC,CAAC,CAACL,IAAI,CAAEM,IAAI,IAAK,IAAI,CAACC,MAAM,CAACD,IAAI,CAAC,CAAC;IAChD,CAAC,CAAC,CACGE,KAAK,CAAEC,GAAG,IAAK;MAChB,IAAI,CAACN,OAAO,CAAC,kBAAkB,EAAEM,GAAG,CAAC;IACzC,CAAC,CAAC;EACN;EACAC,OAAOA,CAACJ,IAAI,EAAEK,QAAQ,EAAE;IACpB,IAAI,CAACZ,MAAM,CAACO,IAAI,CAAC,CACZN,IAAI,CAAEC,GAAG,IAAK;MACf,IAAI,CAACA,GAAG,CAACC,EAAE,EAAE;QACT,OAAO,IAAI,CAACC,OAAO,CAAC,mBAAmB,EAAEF,GAAG,CAACG,MAAM,EAAEH,GAAG,CAAC;MAC7D;MACAU,QAAQ,CAAC,CAAC;IACd,CAAC,CAAC,CACGH,KAAK,CAAEC,GAAG,IAAK;MAChB,IAAI,CAACN,OAAO,CAAC,mBAAmB,EAAEM,GAAG,CAAC;IAC1C,CAAC,CAAC;EACN;EACAV,MAAMA,CAACO,IAAI,EAAE;IACT,IAAIM,EAAE;IACN,MAAMC,MAAM,GAAGP,IAAI,KAAKQ,SAAS;IACjC,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,IAAI,CAACC,IAAI,CAACC,YAAY,CAAC;IACnD,IAAIL,MAAM,EAAE;MACRE,OAAO,CAACI,GAAG,CAAC,cAAc,EAAE,0BAA0B,CAAC;IAC3D;IACA,CAACP,EAAE,GAAG,IAAI,CAACQ,MAAM,CAACC,UAAU,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,aAAa,CAACP,OAAO,CAAC;IAC5F,OAAOQ,KAAK,CAAC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACrBC,MAAM,EAAEZ,MAAM,GAAG,MAAM,GAAG,KAAK;MAC/Ba,IAAI,EAAEb,MAAM,GAAGP,IAAI,GAAG,IAAI;MAC1BS,OAAO;MACPY,WAAW,EAAE,IAAI,CAACV,IAAI,CAACW,eAAe,GAAG,SAAS,GAAG;IACzD,CAAC,CAAC,CAAC5B,IAAI,CAAEC,GAAG,IAAK;MACb,IAAIW,EAAE;MACN;MACA,CAACA,EAAE,GAAG,IAAI,CAACQ,MAAM,CAACC,UAAU,MAAM,IAAI,IAAIT,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,YAAY,CAAC5B,GAAG,CAACc,OAAO,CAACe,YAAY,CAAC,CAAC,CAAC;MAC9G,OAAO7B,GAAG;IACd,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}