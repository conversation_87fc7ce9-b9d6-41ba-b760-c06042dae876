import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  ApiResponse,
  AuthResponse,
  User,
  UserRegistration,
  UserLogin,
  UserUpdate,
  Message,
  MessageSend,
  Room,
  RoomCreate,
  RoomUpdate,
  PaginationResponse
} from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3011/api';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async register(userData: UserRegistration): Promise<AuthResponse> {
    const response = await this.api.post<ApiResponse<AuthResponse>>('/auth/register', userData);
    return response.data.data!;
  }

  async login(credentials: UserLogin): Promise<AuthResponse> {
    const response = await this.api.post<ApiResponse<AuthResponse>>('/auth/login', credentials);
    return response.data.data!;
  }

  async getMe(): Promise<User> {
    const response = await this.api.get<ApiResponse<{ user: User }>>('/auth/me');
    return response.data.data!.user;
  }

  async updateProfile(userData: UserUpdate): Promise<User> {
    const response = await this.api.put<ApiResponse<{ user: User }>>('/auth/profile', userData);
    return response.data.data!.user;
  }

  async changePassword(passwordData: { currentPassword: string; newPassword: string }): Promise<void> {
    await this.api.put('/auth/password', passwordData);
  }

  async logout(): Promise<void> {
    await this.api.post('/auth/logout');
  }

  // User endpoints
  async getUsers(params?: { page?: number; limit?: number; search?: string; status?: string }): Promise<PaginationResponse<User>> {
    const response = await this.api.get<ApiResponse<PaginationResponse<User>>>('/users', { params });
    return response.data.data!;
  }

  async getUserById(userId: string): Promise<User> {
    const response = await this.api.get<ApiResponse<{ user: User }>>(`/users/${userId}`);
    return response.data.data!.user;
  }

  async searchUsers(query: string, limit?: number): Promise<User[]> {
    const response = await this.api.get<ApiResponse<{ users: User[] }>>('/users/search', {
      params: { q: query, limit }
    });
    return response.data.data!.users;
  }

  async getOnlineUsers(): Promise<User[]> {
    const response = await this.api.get<ApiResponse<{ users: User[] }>>('/users/online');
    return response.data.data!.users;
  }

  async updateUserStatus(status: string): Promise<User> {
    const response = await this.api.put<ApiResponse<{ user: User }>>('/users/status', { status });
    return response.data.data!.user;
  }

  async blockUser(userId: string): Promise<void> {
    await this.api.post(`/users/${userId}/block`);
  }

  async unblockUser(userId: string): Promise<void> {
    await this.api.delete(`/users/${userId}/block`);
  }

  async getBlockedUsers(): Promise<User[]> {
    const response = await this.api.get<ApiResponse<{ blockedUsers: User[] }>>('/users/blocked');
    return response.data.data!.blockedUsers;
  }

  // Message endpoints
  async sendMessage(messageData: MessageSend): Promise<Message> {
    const response = await this.api.post<ApiResponse<{ message: Message }>>('/messages', messageData);
    return response.data.data!.message;
  }

  async getConversation(userId: string, page?: number, limit?: number): Promise<PaginationResponse<Message>> {
    const response = await this.api.get<ApiResponse<PaginationResponse<Message>>>(`/messages/conversation/${userId}`, {
      params: { page, limit }
    });
    return response.data.data!;
  }

  async getRoomMessages(roomId: string, page?: number, limit?: number): Promise<PaginationResponse<Message>> {
    const response = await this.api.get<ApiResponse<PaginationResponse<Message>>>(`/messages/room/${roomId}`, {
      params: { page, limit }
    });
    return response.data.data!;
  }

  async editMessage(messageId: string, content: string): Promise<Message> {
    const response = await this.api.put<ApiResponse<{ message: Message }>>(`/messages/${messageId}`, { content });
    return response.data.data!.message;
  }

  async deleteMessage(messageId: string): Promise<void> {
    await this.api.delete(`/messages/${messageId}`);
  }

  async addReaction(messageId: string, emoji: string): Promise<void> {
    await this.api.post(`/messages/${messageId}/reaction`, { emoji });
  }

  async removeReaction(messageId: string): Promise<void> {
    await this.api.delete(`/messages/${messageId}/reaction`);
  }

  async markAsRead(messageId: string): Promise<void> {
    await this.api.put(`/messages/${messageId}/read`);
  }

  // Room endpoints
  async createRoom(roomData: RoomCreate): Promise<Room> {
    const response = await this.api.post<ApiResponse<{ room: Room }>>('/rooms', roomData);
    return response.data.data!.room;
  }

  async getPublicRooms(page?: number, limit?: number): Promise<PaginationResponse<Room>> {
    const response = await this.api.get<ApiResponse<PaginationResponse<Room>>>('/rooms/public', {
      params: { page, limit }
    });
    return response.data.data!;
  }

  async getUserRooms(): Promise<Room[]> {
    const response = await this.api.get<ApiResponse<{ rooms: Room[] }>>('/rooms/my');
    return response.data.data!.rooms;
  }

  async getRoomById(roomId: string): Promise<Room> {
    const response = await this.api.get<ApiResponse<{ room: Room }>>(`/rooms/${roomId}`);
    return response.data.data!.room;
  }

  async updateRoom(roomId: string, roomData: RoomUpdate): Promise<Room> {
    const response = await this.api.put<ApiResponse<{ room: Room }>>(`/rooms/${roomId}`, roomData);
    return response.data.data!.room;
  }

  async deleteRoom(roomId: string): Promise<void> {
    await this.api.delete(`/rooms/${roomId}`);
  }

  async joinRoom(roomId: string): Promise<void> {
    await this.api.post(`/rooms/${roomId}/join`);
  }

  async leaveRoom(roomId: string): Promise<void> {
    await this.api.post(`/rooms/${roomId}/leave`);
  }

  async addRoomMember(roomId: string, userId: string, role?: string): Promise<void> {
    await this.api.post(`/rooms/${roomId}/members`, { userId, role });
  }

  async removeRoomMember(roomId: string, userId: string): Promise<void> {
    await this.api.delete(`/rooms/${roomId}/members/${userId}`);
  }

  async updateMemberRole(roomId: string, userId: string, role: string): Promise<void> {
    await this.api.put(`/rooms/${roomId}/members/${userId}/role`, { role });
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health');
    return response.data;
  }

  // Utility methods
  setAuthToken(token: string): void {
    localStorage.setItem('token', token);
  }

  removeAuthToken(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  getAuthToken(): string | null {
    return localStorage.getItem('token');
  }

  isAuthenticated(): boolean {
    return !!this.getAuthToken();
  }
}

export const apiService = new ApiService();
export default apiService;
