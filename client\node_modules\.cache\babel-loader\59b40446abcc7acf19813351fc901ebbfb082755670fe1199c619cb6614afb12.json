{"ast": null, "code": "/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param year - The year of the date to check\n * @param month - The month of the date to check\n * @param day - The day of the date to check\n *\n * @returns `true` if the date exists\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport function isExists(year, month, day) {\n  const date = new Date(year, month, day);\n  return date.getFullYear() === year && date.getMonth() === month && date.getDate() === day;\n}\n\n// Fallback for modularized imports:\nexport default isExists;", "map": {"version": 3, "names": ["isExists", "year", "month", "day", "date", "Date", "getFullYear", "getMonth", "getDate"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/isExists.js"], "sourcesContent": ["/**\n * @name isExists\n * @category Common Helpers\n * @summary Is the given date exists?\n *\n * @description\n * Checks if the given arguments convert to an existing date.\n *\n * @param year - The year of the date to check\n * @param month - The month of the date to check\n * @param day - The day of the date to check\n *\n * @returns `true` if the date exists\n *\n * @example\n * // For the valid date:\n * const result = isExists(2018, 0, 31)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isExists(2018, 1, 31)\n * //=> false\n */\nexport function isExists(year, month, day) {\n  const date = new Date(year, month, day);\n  return (\n    date.getFullYear() === year &&\n    date.getMonth() === month &&\n    date.getDate() === day\n  );\n}\n\n// Fallback for modularized imports:\nexport default isExists;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACJ,IAAI,EAAEC,KAAK,EAAEC,GAAG,CAAC;EACvC,OACEC,IAAI,CAACE,WAAW,CAAC,CAAC,KAAKL,IAAI,IAC3BG,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAKL,KAAK,IACzBE,IAAI,CAACI,OAAO,CAAC,CAAC,KAAKL,GAAG;AAE1B;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}