import { useState, useEffect, useCallback, useRef } from 'react';
import { socketService } from '../services/socket';
import { debounce } from '../utils';

export const useTyping = (roomId?: string) => {
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced function to stop typing
  const stopTyping = useCallback(
    debounce(() => {
      if (roomId && isTyping) {
        socketService.sendTyping(roomId, false);
        setIsTyping(false);
      }
    }, 1000),
    [roomId, isTyping]
  );

  // Start typing
  const startTyping = useCallback(() => {
    if (roomId && !isTyping) {
      socketService.sendTyping(roomId, true);
      setIsTyping(true);
    }
    stopTyping();
  }, [roomId, isTyping, stopTyping]);

  // Handle typing events from other users
  useEffect(() => {
    const handleUserTyping = (data: { userId: string; username: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        if (data.isTyping) {
          return prev.includes(data.username) ? prev : [...prev, data.username];
        } else {
          return prev.filter(username => username !== data.username);
        }
      });
    };

    socketService.onUserTyping(handleUserTyping);

    return () => {
      socketService.offUserTyping(handleUserTyping);
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (roomId && isTyping) {
        socketService.sendTyping(roomId, false);
      }
    };
  }, [roomId, isTyping]);

  return {
    isTyping,
    typingUsers,
    startTyping,
    stopTyping: () => stopTyping()
  };
};
