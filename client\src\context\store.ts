import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, Message, Room, AuthState, ChatState } from '../types';
import { apiService } from '../services/api';
import { socketService } from '../services/socket';

// Auth Store
interface AuthStore extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  setUser: (user: User) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (emailOrUsername: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiService.login({ emailOrUsername, password });
          const { token, user } = response;
          
          apiService.setAuthToken(token);
          await socketService.connect(token);
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.error?.message || 'Login failed'
          });
          throw error;
        }
      },

      register: async (username: string, email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const response = await apiService.register({ username, email, password });
          const { token, user } = response;
          
          apiService.setAuthToken(token);
          await socketService.connect(token);
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.error?.message || 'Registration failed'
          });
          throw error;
        }
      },

      logout: () => {
        apiService.logout().catch(console.error);
        apiService.removeAuthToken();
        socketService.disconnect();
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        });
      },

      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null });
        try {
          const updatedUser = await apiService.updateProfile(userData);
          set({
            user: updatedUser,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            isLoading: false,
            error: error.response?.data?.error?.message || 'Profile update failed'
          });
          throw error;
        }
      },

      setUser: (user: User) => set({ user }),
      setError: (error: string | null) => set({ error }),
      clearError: () => set({ error: null })
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);

// Chat Store
interface ChatStore extends ChatState {
  // Actions
  setSelectedRoom: (room: Room | null) => void;
  setSelectedConversation: (user: User | null) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: string, updates: Partial<Message>) => void;
  removeMessage: (messageId: string) => void;
  setMessages: (messages: Message[]) => void;
  addMessages: (messages: Message[]) => void;
  setRooms: (rooms: Room[]) => void;
  addRoom: (room: Room) => void;
  updateRoom: (roomId: string, updates: Partial<Room>) => void;
  removeRoom: (roomId: string) => void;
  setOnlineUsers: (users: User[]) => void;
  updateUserStatus: (userId: string, status: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Socket event handlers
  handleNewMessage: (message: Message) => void;
  handleUserStatusUpdate: (data: { userId: string; status: string }) => void;
  handleMessageReaction: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => void;
  
  // API actions
  loadUserRooms: () => Promise<void>;
  loadRoomMessages: (roomId: string) => Promise<void>;
  loadConversation: (userId: string) => Promise<void>;
  sendMessage: (content: string, roomId?: string, recipientId?: string) => Promise<void>;
  joinRoom: (roomId: string) => Promise<void>;
  leaveRoom: (roomId: string) => Promise<void>;
}

export const useChatStore = create<ChatStore>((set, get) => ({
  currentUser: null,
  selectedRoom: null,
  selectedConversation: null,
  messages: [],
  rooms: [],
  onlineUsers: [],
  isLoading: false,
  error: null,

  setSelectedRoom: (room: Room | null) => {
    set({ selectedRoom: room, selectedConversation: null, messages: [] });
    if (room) {
      get().loadRoomMessages(room.id);
      socketService.joinRoom(room.id);
    }
  },

  setSelectedConversation: (user: User | null) => {
    set({ selectedConversation: user, selectedRoom: null, messages: [] });
    if (user) {
      get().loadConversation(user.id);
    }
  },

  addMessage: (message: Message) => {
    set(state => ({
      messages: [...state.messages, message]
    }));
  },

  updateMessage: (messageId: string, updates: Partial<Message>) => {
    set(state => ({
      messages: state.messages.map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    }));
  },

  removeMessage: (messageId: string) => {
    set(state => ({
      messages: state.messages.filter(msg => msg.id !== messageId)
    }));
  },

  setMessages: (messages: Message[]) => set({ messages }),

  addMessages: (messages: Message[]) => {
    set(state => ({
      messages: [...messages, ...state.messages]
    }));
  },

  setRooms: (rooms: Room[]) => set({ rooms }),

  addRoom: (room: Room) => {
    set(state => ({
      rooms: [...state.rooms, room]
    }));
  },

  updateRoom: (roomId: string, updates: Partial<Room>) => {
    set(state => ({
      rooms: state.rooms.map(room =>
        room.id === roomId ? { ...room, ...updates } : room
      )
    }));
  },

  removeRoom: (roomId: string) => {
    set(state => ({
      rooms: state.rooms.filter(room => room.id !== roomId)
    }));
  },

  setOnlineUsers: (users: User[]) => set({ onlineUsers: users }),

  updateUserStatus: (userId: string, status: string) => {
    set(state => ({
      onlineUsers: state.onlineUsers.map(user =>
        user.id === userId ? { ...user, status: status as any } : user
      )
    }));
  },

  setLoading: (loading: boolean) => set({ isLoading: loading }),
  setError: (error: string | null) => set({ error }),
  clearError: () => set({ error: null }),

  // Socket event handlers
  handleNewMessage: (message: Message) => {
    const { selectedRoom, selectedConversation } = get();
    
    // Only add message if it's for the current conversation/room
    if (
      (selectedRoom && message.room?.id === selectedRoom.id) ||
      (selectedConversation && 
        (message.sender.id === selectedConversation.id || message.recipient?.id === selectedConversation.id))
    ) {
      get().addMessage(message);
    }
  },

  handleUserStatusUpdate: (data: { userId: string; status: string }) => {
    get().updateUserStatus(data.userId, data.status);
  },

  handleMessageReaction: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => {
    // Update message reactions
    const { messages } = get();
    const message = messages.find(msg => msg.id === data.messageId);
    if (message) {
      let updatedReactions = [...message.reactions];
      
      if (data.action === 'add') {
        // Remove existing reaction from this user first
        updatedReactions = updatedReactions.filter(r => r.user.id !== data.userId);
        // Add new reaction
        updatedReactions.push({
          user: { id: data.userId } as User,
          emoji: data.emoji,
          createdAt: new Date().toISOString()
        });
      } else {
        // Remove reaction
        updatedReactions = updatedReactions.filter(r => r.user.id !== data.userId);
      }
      
      get().updateMessage(data.messageId, { reactions: updatedReactions });
    }
  },

  // API actions
  loadUserRooms: async () => {
    set({ isLoading: true, error: null });
    try {
      const rooms = await apiService.getUserRooms();
      set({ rooms, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.error?.message || 'Failed to load rooms'
      });
    }
  },

  loadRoomMessages: async (roomId: string) => {
    set({ isLoading: true, error: null });
    try {
      const response = await apiService.getRoomMessages(roomId, 1, 50);
      set({ messages: response.data, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.error?.message || 'Failed to load messages'
      });
    }
  },

  loadConversation: async (userId: string) => {
    set({ isLoading: true, error: null });
    try {
      const response = await apiService.getConversation(userId, 1, 50);
      set({ messages: response.data, isLoading: false });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.response?.data?.error?.message || 'Failed to load conversation'
      });
    }
  },

  sendMessage: async (content: string, roomId?: string, recipientId?: string) => {
    try {
      const messageData = {
        content,
        ...(roomId && { room: roomId }),
        ...(recipientId && { recipient: recipientId })
      };
      
      // Send via socket for real-time delivery
      socketService.sendMessage({
        content,
        roomId,
        recipientId
      });
      
    } catch (error: any) {
      set({
        error: error.response?.data?.error?.message || 'Failed to send message'
      });
    }
  },

  joinRoom: async (roomId: string) => {
    try {
      await apiService.joinRoom(roomId);
      socketService.joinRoom(roomId);
      // Reload rooms to get updated membership
      await get().loadUserRooms();
    } catch (error: any) {
      set({
        error: error.response?.data?.error?.message || 'Failed to join room'
      });
    }
  },

  leaveRoom: async (roomId: string) => {
    try {
      await apiService.leaveRoom(roomId);
      socketService.leaveRoom(roomId);
      // Remove room from local state
      get().removeRoom(roomId);
    } catch (error: any) {
      set({
        error: error.response?.data?.error?.message || 'Failed to leave room'
      });
    }
  }
}));
