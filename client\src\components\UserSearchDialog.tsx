import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  TextField,
  List,
  ListItem,
  ListItemButton,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Box,
  CircularProgress,
  InputAdornment,
  Badge,
  IconButton,
  Chip,
} from '@mui/material';
import {
  SearchOutlined,
  CloseOutlined,
  MessageOutlined,
  PersonAddOutlined,
} from '@mui/icons-material';

import { apiService } from '../services/api';
import { useChatStore } from '../context/store';
import { generateInitials, generateAvatarColor, debounce } from '../utils';
import { User } from '../types';

interface UserSearchDialogProps {
  open: boolean;
  onClose: () => void;
}

const UserSearchDialog: React.FC<UserSearchDialogProps> = ({ open, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { setSelectedConversation, onlineUsers } = useChatStore();

  // Debounced search function
  const debouncedSearch = debounce(async (query: string) => {
    if (query.trim().length < 2) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const users = await apiService.searchUsers(query.trim(), 20);
      setSearchResults(users);
    } catch (error: any) {
      setError('Failed to search users');
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  }, 300);

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleUserSelect = (user: User) => {
    setSelectedConversation(user);
    handleClose();
  };

  const handleClose = () => {
    setSearchQuery('');
    setSearchResults([]);
    setError(null);
    onClose();
  };

  const isUserOnline = (userId: string) => {
    return onlineUsers.some(user => user.id === userId);
  };

  const getUserStatus = (userId: string) => {
    const user = onlineUsers.find(u => u.id === userId);
    return user?.status || 'offline';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return '#4caf50';
      case 'away':
        return '#ff9800';
      case 'busy':
        return '#f44336';
      case 'offline':
      default:
        return '#9e9e9e';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2, height: '70vh' },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Find Users</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseOutlined />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {/* Search Input */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            fullWidth
            placeholder="Search by username or email..."
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchOutlined color="action" />
                </InputAdornment>
              ),
              endAdornment: isLoading && (
                <InputAdornment position="end">
                  <CircularProgress size={20} />
                </InputAdornment>
              ),
            }}
            variant="outlined"
            size="small"
            autoFocus
          />
        </Box>

        {/* Search Results */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          {error && (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography color="error" variant="body2">
                {error}
              </Typography>
            </Box>
          )}

          {!error && searchQuery.trim().length === 0 && (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <SearchOutlined sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Start typing to search for users
              </Typography>
            </Box>
          )}

          {!error && searchQuery.trim().length > 0 && searchQuery.trim().length < 2 && (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Type at least 2 characters to search
              </Typography>
            </Box>
          )}

          {!error && searchQuery.trim().length >= 2 && !isLoading && searchResults.length === 0 && (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No users found matching "{searchQuery}"
              </Typography>
            </Box>
          )}

          {searchResults.length > 0 && (
            <List sx={{ py: 0 }}>
              {searchResults.map((user) => {
                const userStatus = getUserStatus(user.id);
                const online = isUserOnline(user.id);
                
                return (
                  <ListItem key={user.id} disablePadding>
                    <ListItemButton
                      onClick={() => handleUserSelect(user)}
                      sx={{
                        py: 1.5,
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Badge
                          overlap="circular"
                          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                          variant="dot"
                          sx={{
                            '& .MuiBadge-badge': {
                              backgroundColor: getStatusColor(userStatus),
                              color: getStatusColor(userStatus),
                              '&::after': {
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                borderRadius: '50%',
                                animation: online ? 'ripple 1.2s infinite ease-in-out' : 'none',
                                border: '1px solid currentColor',
                                content: '""',
                              },
                            },
                            '@keyframes ripple': {
                              '0%': {
                                transform: 'scale(.8)',
                                opacity: 1,
                              },
                              '100%': {
                                transform: 'scale(2.4)',
                                opacity: 0,
                              },
                            },
                          }}
                        >
                          <Avatar
                            sx={{
                              bgcolor: generateAvatarColor(user.username),
                              width: 40,
                              height: 40,
                            }}
                            src={user.avatar}
                          >
                            {!user.avatar && generateInitials(user.username)}
                          </Avatar>
                        </Badge>
                      </ListItemAvatar>
                      
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2">
                              {user.username}
                            </Typography>
                            {online && (
                              <Chip
                                label={userStatus}
                                size="small"
                                sx={{
                                  height: 16,
                                  fontSize: '0.6875rem',
                                  bgcolor: getStatusColor(userStatus),
                                  color: 'white',
                                  '& .MuiChip-label': {
                                    px: 1,
                                  },
                                }}
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary" noWrap>
                            {user.email}
                          </Typography>
                        }
                      />
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleUserSelect(user);
                          }}
                          sx={{
                            bgcolor: 'primary.main',
                            color: 'primary.contrastText',
                            '&:hover': {
                              bgcolor: 'primary.dark',
                            },
                          }}
                        >
                          <MessageOutlined fontSize="small" />
                        </IconButton>
                      </Box>
                    </ListItemButton>
                  </ListItem>
                );
              })}
            </List>
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default UserSearchDialog;
