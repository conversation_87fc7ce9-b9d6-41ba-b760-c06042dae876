{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from \"../useThemeProps/index.js\";\nimport systemStyled from \"../styled/index.js\";\nimport createTheme from \"../createTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON>i<PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => ({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    ...(!ownerState.disableGutters && {\n      paddingLeft: theme.spacing(2),\n      paddingRight: theme.spacing(2),\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('sm')]: {\n        paddingLeft: theme.spacing(3),\n        paddingRight: theme.spacing(3)\n      }\n    })\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => ({\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ...(ownerState.maxWidth === 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('xs')]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n      }\n    }),\n    ...(ownerState.maxWidth &&\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ownerState.maxWidth !== 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up(ownerState.maxWidth)]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n      }\n    })\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n      className,\n      component = 'div',\n      disableGutters = false,\n      fixed = false,\n      maxWidth = 'lg',\n      classes: classesProp,\n      ...other\n    } = props;\n    const ownerState = {\n      ...props,\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    };\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (/*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, {\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref,\n        ...other\n      })\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "generateUtilityClass", "composeClasses", "capitalize", "useThemePropsSystem", "systemStyled", "createTheme", "jsx", "_jsx", "defaultTheme", "defaultCreateStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "String", "max<PERSON><PERSON><PERSON>", "fixed", "disableGutters", "useThemePropsDefault", "inProps", "useUtilityClasses", "componentName", "getContainerUtilityClass", "classes", "slots", "createContainer", "options", "createStyledComponent", "useThemeProps", "ContainerRoot", "theme", "width", "marginLeft", "boxSizing", "marginRight", "paddingLeft", "spacing", "paddingRight", "breakpoints", "up", "Object", "keys", "values", "reduce", "acc", "breakpoint<PERSON><PERSON><PERSON><PERSON><PERSON>", "breakpoint", "value", "unit", "Math", "max", "xs", "Container", "forwardRef", "ref", "className", "component", "classesProp", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "bool", "oneOfType", "oneOf", "sx", "arrayOf", "func"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/Container/createContainer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '@mui/utils/capitalize';\nimport useThemePropsSystem from \"../useThemeProps/index.js\";\nimport systemStyled from \"../styled/index.js\";\nimport createTheme from \"../createTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultTheme = createTheme();\nconst defaultCreateStyledComponent = systemStyled('div', {\n  name: '<PERSON>i<PERSON>ontainer',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n  }\n});\nconst useThemePropsDefault = inProps => useThemePropsSystem({\n  props: inProps,\n  name: 'MuiContainer',\n  defaultTheme\n});\nconst useUtilityClasses = (ownerState, componentName) => {\n  const getContainerUtilityClass = slot => {\n    return generateUtilityClass(componentName, slot);\n  };\n  const {\n    classes,\n    fixed,\n    disableGutters,\n    maxWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', maxWidth && `maxWidth${capitalize(String(maxWidth))}`, fixed && 'fixed', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getContainerUtilityClass, classes);\n};\nexport default function createContainer(options = {}) {\n  const {\n    // This will allow adding custom styled fn (for example for custom sx style function)\n    createStyledComponent = defaultCreateStyledComponent,\n    useThemeProps = useThemePropsDefault,\n    componentName = 'MuiContainer'\n  } = options;\n  const ContainerRoot = createStyledComponent(({\n    theme,\n    ownerState\n  }) => ({\n    width: '100%',\n    marginLeft: 'auto',\n    boxSizing: 'border-box',\n    marginRight: 'auto',\n    ...(!ownerState.disableGutters && {\n      paddingLeft: theme.spacing(2),\n      paddingRight: theme.spacing(2),\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('sm')]: {\n        paddingLeft: theme.spacing(3),\n        paddingRight: theme.spacing(3)\n      }\n    })\n  }), ({\n    theme,\n    ownerState\n  }) => ownerState.fixed && Object.keys(theme.breakpoints.values).reduce((acc, breakpointValueKey) => {\n    const breakpoint = breakpointValueKey;\n    const value = theme.breakpoints.values[breakpoint];\n    if (value !== 0) {\n      // @ts-ignore\n      acc[theme.breakpoints.up(breakpoint)] = {\n        maxWidth: `${value}${theme.breakpoints.unit}`\n      };\n    }\n    return acc;\n  }, {}), ({\n    theme,\n    ownerState\n  }) => ({\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ...(ownerState.maxWidth === 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up('xs')]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: Math.max(theme.breakpoints.values.xs, 444)\n      }\n    }),\n    ...(ownerState.maxWidth &&\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    ownerState.maxWidth !== 'xs' && {\n      // @ts-ignore module augmentation fails if custom breakpoints are used\n      [theme.breakpoints.up(ownerState.maxWidth)]: {\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        maxWidth: `${theme.breakpoints.values[ownerState.maxWidth]}${theme.breakpoints.unit}`\n      }\n    })\n  }));\n  const Container = /*#__PURE__*/React.forwardRef(function Container(inProps, ref) {\n    const props = useThemeProps(inProps);\n    const {\n      className,\n      component = 'div',\n      disableGutters = false,\n      fixed = false,\n      maxWidth = 'lg',\n      classes: classesProp,\n      ...other\n    } = props;\n    const ownerState = {\n      ...props,\n      component,\n      disableGutters,\n      fixed,\n      maxWidth\n    };\n\n    // @ts-ignore module augmentation fails if custom breakpoints are used\n    const classes = useUtilityClasses(ownerState, componentName);\n    return (\n      /*#__PURE__*/\n      // @ts-ignore theme is injected by the styled util\n      _jsx(ContainerRoot, {\n        as: component\n        // @ts-ignore module augmentation fails if custom breakpoints are used\n        ,\n        ownerState: ownerState,\n        className: clsx(classes.root, className),\n        ref: ref,\n        ...other\n      })\n    );\n  });\n  process.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n    children: PropTypes.node,\n    classes: PropTypes.object,\n    className: PropTypes.string,\n    component: PropTypes.elementType,\n    disableGutters: PropTypes.bool,\n    fixed: PropTypes.bool,\n    maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n  } : void 0;\n  return Container;\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAGH,WAAW,CAAC,CAAC;AAClC,MAAMI,4BAA4B,GAAGL,YAAY,CAAC,KAAK,EAAE;EACvDM,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJC;IACF,CAAC,GAAGF,KAAK;IACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAEF,MAAM,CAAC,WAAWZ,UAAU,CAACe,MAAM,CAACF,UAAU,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEH,UAAU,CAACI,KAAK,IAAIL,MAAM,CAACK,KAAK,EAAEJ,UAAU,CAACK,cAAc,IAAIN,MAAM,CAACM,cAAc,CAAC;EAC1K;AACF,CAAC,CAAC;AACF,MAAMC,oBAAoB,GAAGC,OAAO,IAAInB,mBAAmB,CAAC;EAC1DU,KAAK,EAAES,OAAO;EACdZ,IAAI,EAAE,cAAc;EACpBF;AACF,CAAC,CAAC;AACF,MAAMe,iBAAiB,GAAGA,CAACR,UAAU,EAAES,aAAa,KAAK;EACvD,MAAMC,wBAAwB,GAAGd,IAAI,IAAI;IACvC,OAAOX,oBAAoB,CAACwB,aAAa,EAAEb,IAAI,CAAC;EAClD,CAAC;EACD,MAAM;IACJe,OAAO;IACPP,KAAK;IACLC,cAAc;IACdF;EACF,CAAC,GAAGH,UAAU;EACd,MAAMY,KAAK,GAAG;IACZX,IAAI,EAAE,CAAC,MAAM,EAAEE,QAAQ,IAAI,WAAWhB,UAAU,CAACe,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,EAAEC,KAAK,IAAI,OAAO,EAAEC,cAAc,IAAI,gBAAgB;EAC5H,CAAC;EACD,OAAOnB,cAAc,CAAC0B,KAAK,EAAEF,wBAAwB,EAAEC,OAAO,CAAC;AACjE,CAAC;AACD,eAAe,SAASE,eAAeA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpD,MAAM;IACJ;IACAC,qBAAqB,GAAGrB,4BAA4B;IACpDsB,aAAa,GAAGV,oBAAoB;IACpCG,aAAa,GAAG;EAClB,CAAC,GAAGK,OAAO;EACX,MAAMG,aAAa,GAAGF,qBAAqB,CAAC,CAAC;IAC3CG,KAAK;IACLlB;EACF,CAAC,MAAM;IACLmB,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,MAAM;IACnB,IAAI,CAACtB,UAAU,CAACK,cAAc,IAAI;MAChCkB,WAAW,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;MAC7BC,YAAY,EAAEP,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;MAC9B;MACA,CAACN,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5BJ,WAAW,EAAEL,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC;QAC7BC,YAAY,EAAEP,KAAK,CAACM,OAAO,CAAC,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,CAAC,EAAE,CAAC;IACHN,KAAK;IACLlB;EACF,CAAC,KAAKA,UAAU,CAACI,KAAK,IAAIwB,MAAM,CAACC,IAAI,CAACX,KAAK,CAACQ,WAAW,CAACI,MAAM,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,kBAAkB,KAAK;IAClG,MAAMC,UAAU,GAAGD,kBAAkB;IACrC,MAAME,KAAK,GAAGjB,KAAK,CAACQ,WAAW,CAACI,MAAM,CAACI,UAAU,CAAC;IAClD,IAAIC,KAAK,KAAK,CAAC,EAAE;MACf;MACAH,GAAG,CAACd,KAAK,CAACQ,WAAW,CAACC,EAAE,CAACO,UAAU,CAAC,CAAC,GAAG;QACtC/B,QAAQ,EAAE,GAAGgC,KAAK,GAAGjB,KAAK,CAACQ,WAAW,CAACU,IAAI;MAC7C,CAAC;IACH;IACA,OAAOJ,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACPd,KAAK;IACLlB;EACF,CAAC,MAAM;IACL;IACA,IAAIA,UAAU,CAACG,QAAQ,KAAK,IAAI,IAAI;MAClC;MACA,CAACe,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC,IAAI,CAAC,GAAG;QAC5B;QACAxB,QAAQ,EAAEkC,IAAI,CAACC,GAAG,CAACpB,KAAK,CAACQ,WAAW,CAACI,MAAM,CAACS,EAAE,EAAE,GAAG;MACrD;IACF,CAAC,CAAC;IACF,IAAIvC,UAAU,CAACG,QAAQ;IACvB;IACAH,UAAU,CAACG,QAAQ,KAAK,IAAI,IAAI;MAC9B;MACA,CAACe,KAAK,CAACQ,WAAW,CAACC,EAAE,CAAC3B,UAAU,CAACG,QAAQ,CAAC,GAAG;QAC3C;QACAA,QAAQ,EAAE,GAAGe,KAAK,CAACQ,WAAW,CAACI,MAAM,CAAC9B,UAAU,CAACG,QAAQ,CAAC,GAAGe,KAAK,CAACQ,WAAW,CAACU,IAAI;MACrF;IACF,CAAC;EACH,CAAC,CAAC,CAAC;EACH,MAAMI,SAAS,GAAG,aAAa1D,KAAK,CAAC2D,UAAU,CAAC,SAASD,SAASA,CAACjC,OAAO,EAAEmC,GAAG,EAAE;IAC/E,MAAM5C,KAAK,GAAGkB,aAAa,CAACT,OAAO,CAAC;IACpC,MAAM;MACJoC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBvC,cAAc,GAAG,KAAK;MACtBD,KAAK,GAAG,KAAK;MACbD,QAAQ,GAAG,IAAI;MACfQ,OAAO,EAAEkC,WAAW;MACpB,GAAGC;IACL,CAAC,GAAGhD,KAAK;IACT,MAAME,UAAU,GAAG;MACjB,GAAGF,KAAK;MACR8C,SAAS;MACTvC,cAAc;MACdD,KAAK;MACLD;IACF,CAAC;;IAED;IACA,MAAMQ,OAAO,GAAGH,iBAAiB,CAACR,UAAU,EAAES,aAAa,CAAC;IAC5D,QACE;MACA;MACAjB,IAAI,CAACyB,aAAa,EAAE;QAClB8B,EAAE,EAAEH;QACJ;QAAA;;QAEA5C,UAAU,EAAEA,UAAU;QACtB2C,SAAS,EAAE3D,IAAI,CAAC2B,OAAO,CAACV,IAAI,EAAE0C,SAAS,CAAC;QACxCD,GAAG,EAAEA,GAAG;QACR,GAAGI;MACL,CAAC;IAAC;EAEN,CAAC,CAAC;EACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,SAAS,CAACW,SAAS,CAAC,yBAAyB;IACnFC,QAAQ,EAAErE,SAAS,CAACsE,IAAI;IACxB1C,OAAO,EAAE5B,SAAS,CAACuE,MAAM;IACzBX,SAAS,EAAE5D,SAAS,CAACwE,MAAM;IAC3BX,SAAS,EAAE7D,SAAS,CAACyE,WAAW;IAChCnD,cAAc,EAAEtB,SAAS,CAAC0E,IAAI;IAC9BrD,KAAK,EAAErB,SAAS,CAAC0E,IAAI;IACrBtD,QAAQ,EAAEpB,SAAS,CAAC,sCAAsC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC4E,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE5E,SAAS,CAACwE,MAAM,CAAC,CAAC;IAC/IK,EAAE,EAAE7E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC8E,OAAO,CAAC9E,SAAS,CAAC2E,SAAS,CAAC,CAAC3E,SAAS,CAAC+E,IAAI,EAAE/E,SAAS,CAACuE,MAAM,EAAEvE,SAAS,CAAC0E,IAAI,CAAC,CAAC,CAAC,EAAE1E,SAAS,CAAC+E,IAAI,EAAE/E,SAAS,CAACuE,MAAM,CAAC;EACxJ,CAAC,GAAG,KAAK,CAAC;EACV,OAAOd,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}