{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst RadioGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  RadioGroupContext.displayName = 'RadioGroupContext';\n}\nexport default RadioGroupContext;", "map": {"version": 3, "names": ["React", "RadioGroupContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/material/esm/RadioGroup/RadioGroupContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst RadioGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  RadioGroupContext.displayName = 'RadioGroupContext';\n}\nexport default RadioGroupContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACrE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,iBAAiB,CAACM,WAAW,GAAG,mBAAmB;AACrD;AACA,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}