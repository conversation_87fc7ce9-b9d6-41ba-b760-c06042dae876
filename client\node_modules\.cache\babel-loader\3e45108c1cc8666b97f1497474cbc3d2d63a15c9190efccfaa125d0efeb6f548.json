{"ast": null, "code": "export { default } from \"./elementTypeAcceptingRef.js\";", "map": {"version": 3, "names": ["default"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/utils/esm/elementTypeAcceptingRef/index.js"], "sourcesContent": ["export { default } from \"./elementTypeAcceptingRef.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}