{"ast": null, "code": "'use strict';\n\nvar globalThis = require('../internals/global-this');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(globalThis, key, {\n      value: value,\n      configurable: true,\n      writable: true\n    });\n  } catch (error) {\n    globalThis[key] = value;\n  }\n  return value;\n};", "map": {"version": 3, "names": ["globalThis", "require", "defineProperty", "Object", "module", "exports", "key", "value", "configurable", "writable", "error"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/core-js-pure/internals/define-global-property.js"], "sourcesContent": ["'use strict';\nvar globalThis = require('../internals/global-this');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(globalThis, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    globalThis[key] = value;\n  } return value;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,0BAA0B,CAAC;;AAEpD;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAAc;AAE1CE,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;EACrC,IAAI;IACFL,cAAc,CAACF,UAAU,EAAEM,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EACvF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdV,UAAU,CAACM,GAAG,CAAC,GAAGC,KAAK;EACzB;EAAE,OAAOA,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}