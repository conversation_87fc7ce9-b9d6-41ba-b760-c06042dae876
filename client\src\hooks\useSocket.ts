import { useEffect, useRef } from 'react';
import { socketService } from '../services/socket';
import { useAuthStore } from '../context/store';
import { Message, Room, User } from '../types';

export const useSocket = () => {
  const { token, isAuthenticated } = useAuthStore();
  const isConnectedRef = useRef(false);

  useEffect(() => {
    if (isAuthenticated && token && !isConnectedRef.current) {
      socketService.connect(token)
        .then(() => {
          isConnectedRef.current = true;
          console.log('Socket connected successfully');
        })
        .catch((error) => {
          console.error('Socket connection failed:', error);
        });
    }

    return () => {
      if (isConnectedRef.current) {
        socketService.disconnect();
        isConnectedRef.current = false;
      }
    };
  }, [isAuthenticated, token]);

  return {
    isConnected: socketService.isConnected(),
    socket: socketService
  };
};

export const useSocketEvents = () => {
  const socket = socketService;

  const onNewMessage = (callback: (message: Message) => void) => {
    socket.onNewMessage(callback);
    return () => socket.offNewMessage(callback);
  };

  const onUserStatusUpdate = (callback: (data: { userId: string; username: string; status: string }) => void) => {
    socket.onUserStatusUpdate(callback);
    return () => socket.offUserStatusUpdate(callback);
  };

  const onUserTyping = (callback: (data: { userId: string; username: string; isTyping: boolean }) => void) => {
    socket.onUserTyping(callback);
    return () => socket.offUserTyping(callback);
  };

  const onMessageReaction = (callback: (data: { messageId: string; userId: string; emoji: string; action: 'add' | 'remove' }) => void) => {
    socket.onMessageReaction(callback);
    return () => socket.offMessageReaction(callback);
  };

  const onUserRooms = (callback: (rooms: Room[]) => void) => {
    socket.onUserRooms(callback);
    return () => socket.offUserRooms(callback);
  };

  const onRoomMessages = (callback: (data: { roomId: string; messages: Message[] }) => void) => {
    socket.onRoomMessages(callback);
    return () => socket.offRoomMessages(callback);
  };

  const onOnlineUsers = (callback: (users: User[]) => void) => {
    socket.onOnlineUsers(callback);
    return () => socket.offOnlineUsers(callback);
  };

  const onError = (callback: (data: { message: string }) => void) => {
    socket.onError(callback);
    return () => socket.offError(callback);
  };

  return {
    onNewMessage,
    onUserStatusUpdate,
    onUserTyping,
    onMessageReaction,
    onUserRooms,
    onRoomMessages,
    onOnlineUsers,
    onError
  };
};
