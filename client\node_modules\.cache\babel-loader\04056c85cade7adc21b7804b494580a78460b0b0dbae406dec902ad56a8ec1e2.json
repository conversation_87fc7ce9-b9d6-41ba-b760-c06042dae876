{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameQuarter } from \"./isSameQuarter.js\";\n\n/**\n * The {@link isThisQuarter} function options.\n */\n\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this quarter\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisQuarter(date, options) {\n  return isSameQuarter(constructFrom(options?.in || date, date), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isThisQuarter;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "isSameQuarter", "isThisQuarter", "date", "options", "in"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/isThisQuarter.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameQuarter } from \"./isSameQuarter.js\";\n\n/**\n * The {@link isThisQuarter} function options.\n */\n\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this quarter\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisQuarter(date, options) {\n  return isSameQuarter(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisQuarter;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,OAAOH,aAAa,CAClBF,aAAa,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,EAAEA,IAAI,CAAC,EACxCH,YAAY,CAACI,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAClC,CAAC;AACH;;AAEA;AACA,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}