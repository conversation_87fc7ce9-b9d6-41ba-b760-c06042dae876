import React, { useEffect } from 'react';
import { Box, Paper, Typography, AppBar, Toolbar, IconButton, Avatar, Menu, MenuItem } from '@mui/material';
import { LogoutOutlined, SettingsOutlined, AccountCircleOutlined } from '@mui/icons-material';
import { useState } from 'react';
import toast from 'react-hot-toast';

import { useAuthStore, useChatStore } from '../context/store';
import { useSocket, useSocketEvents } from '../hooks';
import { generateInitials, generateAvatarColor } from '../utils';

// Components
import Sidebar from '../components/Sidebar';
import ChatArea from '../components/ChatArea';
import UserList from '../components/UserList';
import NotificationSystem from '../components/NotificationSystem';

const ChatPage: React.FC = () => {
  const { user, logout } = useAuthStore();
  const { 
    loadUserRooms, 
    setOnlineUsers, 
    handleNewMessage, 
    handleUserStatusUpdate, 
    handleMessageReaction 
  } = useChatStore();
  
  const { isConnected } = useSocket();
  const socketEvents = useSocketEvents();
  
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Load initial data
  useEffect(() => {
    if (isConnected) {
      loadUserRooms();
    }
  }, [isConnected, loadUserRooms]);

  // Set up socket event listeners
  useEffect(() => {
    if (!isConnected) return;

    const unsubscribers = [
      socketEvents.onNewMessage(handleNewMessage),
      socketEvents.onUserStatusUpdate(handleUserStatusUpdate),
      socketEvents.onMessageReaction(handleMessageReaction),
      socketEvents.onOnlineUsers(setOnlineUsers),
      socketEvents.onError((data) => {
        toast.error(data.message);
      }),
    ];

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [isConnected, socketEvents, handleNewMessage, handleUserStatusUpdate, handleMessageReaction, setOnlineUsers]);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleMenuClose();
    toast.success('Logged out successfully');
  };

  if (!user) {
    return null;
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <img
              src="/Funnectologo.png"
              alt="Funnecto Logo"
              style={{
                height: '32px',
                width: 'auto',
                marginRight: '12px'
              }}
            />
            <Typography variant="h6" component="div">
              Funnecto
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationSystem />

            <Typography variant="body2" sx={{ mr: 1 }}>
              {user.username}
            </Typography>

            <IconButton
              onClick={handleMenuClick}
              size="small"
              sx={{ ml: 2 }}
              aria-controls={open ? 'account-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
            >
              <Avatar
                sx={{
                  width: 32,
                  height: 32,
                  bgcolor: generateAvatarColor(user.username),
                  fontSize: '0.875rem',
                }}
                src={user.avatar}
              >
                {!user.avatar && generateInitials(user.username)}
              </Avatar>
            </IconButton>
          </Box>

          <Menu
            anchorEl={anchorEl}
            id="account-menu"
            open={open}
            onClose={handleMenuClose}
            onClick={handleMenuClose}
            PaperProps={{
              elevation: 0,
              sx: {
                overflow: 'visible',
                filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                mt: 1.5,
                '& .MuiAvatar-root': {
                  width: 32,
                  height: 32,
                  ml: -0.5,
                  mr: 1,
                },
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  right: 14,
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: 'translateY(-50%) rotate(45deg)',
                  zIndex: 0,
                },
              },
            }}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={handleMenuClose}>
              <AccountCircleOutlined sx={{ mr: 1 }} />
              Profile
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <SettingsOutlined sx={{ mr: 1 }} />
              Settings
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <LogoutOutlined sx={{ mr: 1 }} />
              Logout
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <Box sx={{ display: 'flex', height: '100%' }}>
          {/* Sidebar - Rooms and Conversations */}
          <Box sx={{ width: '300px', borderRight: 1, borderColor: 'divider' }}>
            <Paper
              sx={{
                height: '100%',
                borderRadius: 0,
              }}
            >
              <Sidebar />
            </Paper>
          </Box>

          {/* Chat Area */}
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <ChatArea />
          </Box>

          {/* User List */}
          <Box sx={{ width: '250px', borderLeft: 1, borderColor: 'divider' }}>
            <Paper
              sx={{
                height: '100%',
                borderRadius: 0,
              }}
            >
              <UserList />
            </Paper>
          </Box>
        </Box>
      </Box>

      {/* Connection Status */}
      {!isConnected && (
        <Box
          sx={{
            position: 'fixed',
            bottom: 16,
            left: 16,
            bgcolor: 'error.main',
            color: 'white',
            px: 2,
            py: 1,
            borderRadius: 1,
            zIndex: 1000,
          }}
        >
          <Typography variant="body2">
            Disconnected - Attempting to reconnect...
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ChatPage;
