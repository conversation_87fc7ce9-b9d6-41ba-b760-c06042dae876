{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 8h-2v2c0 .55-.45 1-1 1H7v2h6c.55 0 1 .45 1 1v3h1c.9 0 1.64.58 1.9 1.39C18.2 16.97 19 15.08 19 13c0-.34-.04-.67-.08-1H17c-1.65 0-3-1.35-3-3V6c0 1.1-.9 2-2 2m-4 9v-1l-4.79-4.79C3.08 11.79 3 12.38 3 13c0 4.08 3.05 7.44 7 7.93V19c-1.1 0-2-.9-2-2\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M18.92 12c.04.33.08.66.08 1 0 2.08-.8 3.97-2.1 5.39-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H7v-2h2c.55 0 1-.45 1-1V8h2c1.1 0 2-.9 2-2V3.46c-.95-.3-1.95-.46-3-.46C5.48 3 1 7.48 1 13s4.48 10 10 10 10-4.48 10-10c0-.34-.02-.67-.05-1zM10 20.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L8 16v1c0 1.1.9 2 2 2zM22 4v-.5C22 2.12 20.88 1 19.5 1S17 2.12 17 3.5V4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1V5c0-.55-.45-1-1-1m-1 0h-3v-.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5z\"\n}, \"1\")], 'VpnLockTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/icons-material/esm/VpnLockTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 8h-2v2c0 .55-.45 1-1 1H7v2h6c.55 0 1 .45 1 1v3h1c.9 0 1.64.58 1.9 1.39C18.2 16.97 19 15.08 19 13c0-.34-.04-.67-.08-1H17c-1.65 0-3-1.35-3-3V6c0 1.1-.9 2-2 2m-4 9v-1l-4.79-4.79C3.08 11.79 3 12.38 3 13c0 4.08 3.05 7.44 7 7.93V19c-1.1 0-2-.9-2-2\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M18.92 12c.04.33.08.66.08 1 0 2.08-.8 3.97-2.1 5.39-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H7v-2h2c.55 0 1-.45 1-1V8h2c1.1 0 2-.9 2-2V3.46c-.95-.3-1.95-.46-3-.46C5.48 3 1 7.48 1 13s4.48 10 10 10 10-4.48 10-10c0-.34-.02-.67-.05-1zM10 20.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L8 16v1c0 1.1.9 2 2 2zM22 4v-.5C22 2.12 20.88 1 19.5 1S17 2.12 17 3.5V4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h5c.55 0 1-.45 1-1V5c0-.55-.45-1-1-1m-1 0h-3v-.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5z\"\n}, \"1\")], 'VpnLockTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,uPAAuP;EAC1PC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}