{"ast": null, "code": "// src/utils/asArray.ts\nfunction asArray(v) {\n  return [].concat(v);\n}\n\n// src/utils/is.ts\nfunction isPsuedoSelector(selector) {\n  return selector.startsWith(\":\");\n}\nfunction isStyleCondition(selector) {\n  return isString(selector) && (selector === \"*\" || selector.length > 1 && \":>~.+*\".includes(selector.slice(0, 1)) || isImmediatePostcondition(selector));\n}\nfunction isValidProperty(property, value) {\n  return (isString(value) || typeof value === \"number\") && !isCssVariables(property) && !isPsuedoSelector(property) && !isMediaQuery(property);\n}\nfunction isMediaQuery(selector) {\n  return selector.startsWith(\"@media\");\n}\nfunction isDirectClass(selector) {\n  return selector === \".\";\n}\nfunction isCssVariables(selector) {\n  return selector === \"--\";\n}\nfunction isString(value) {\n  return value + \"\" === value;\n}\nfunction isImmediatePostcondition(value) {\n  return isString(value) && (value.startsWith(\"&\") || isPsuedoSelector(value));\n}\n\n// src/utils/joinTruthy.ts\nfunction joinTruthy(arr, delimiter = \"\") {\n  return arr.filter(Boolean).join(delimiter);\n}\n\n// src/utils/stableHash.ts\nfunction stableHash(prefix, seed) {\n  let hash = 0;\n  if (seed.length === 0) return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash;\n  }\n  return `${prefix ?? \"cl\"}_${hash.toString(36)}`;\n}\n\n// src/utils/stringManipulators.ts\nfunction handlePropertyValue(property, value) {\n  if (property === \"content\") {\n    return `\"${value}\"`;\n  }\n  return value;\n}\nfunction camelCaseToDash(str) {\n  return str.replace(/([a-z])([A-Z])/g, \"$1-$2\").toLowerCase();\n}\nfunction joinedProperty(property, value) {\n  return `${property}:${value}`;\n}\nfunction toClass(str) {\n  return str ? `.${str}` : \"\";\n}\nfunction appendString(base, line) {\n  return base ? `${base}\n${line}` : line;\n}\n\n// src/Rule.ts\nvar Rule = class _Rule {\n  constructor(sheet, property, value, selector) {\n    this.sheet = sheet;\n    this.property = property;\n    this.value = value;\n    this.selector = selector;\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(this.selector.postconditions);\n    this.hash = this.selector.hasConditions ? this.selector.scopeClassName : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n  toString() {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash\n    });\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors\n    });\n    return `${selectors} {${_Rule.genRule(this.property, this.value)}}`;\n  }\n  static genRule(property, value) {\n    const transformedProperty = camelCaseToDash(property);\n    return joinedProperty(transformedProperty, handlePropertyValue(property, value)) + \";\";\n  }\n};\nfunction mergeSelectors(selectors, {\n  left = \"\",\n  right = \"\"\n} = {}) {\n  const output = selectors.reduce((selectors2, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors2 + current;\n    }\n    if (isImmediatePostcondition(current)) {\n      return selectors2 + current.slice(1);\n    }\n    return joinTruthy([selectors2, current], \" \");\n  }, left);\n  return joinTruthy([output, toClass(right)], \" \");\n}\nvar Selector = class _Selector {\n  constructor(sheet, scopeName = null, {\n    preconditions,\n    postconditions\n  } = {}) {\n    this.sheet = sheet;\n    this.preconditions = [];\n    this.scopeClassName = null;\n    this.scopeName = null;\n    this.postconditions = [];\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n  setScope(scopeName) {\n    if (!scopeName) {\n      return this;\n    }\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(this.sheet.name,\n      // adding the count guarantees uniqueness across style.create calls\n      scopeName + this.sheet.count);\n    }\n    return this;\n  }\n  get hasConditions() {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n  addScope(scopeName) {\n    return new _Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions\n    });\n  }\n  addPrecondition(precondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition)\n    });\n  }\n  addPostcondition(postcondition) {\n    return new _Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition)\n    });\n  }\n  createRule(property, value) {\n    return new Rule(this.sheet, property, value, this);\n  }\n};\n\n// src/Sheet.ts\nvar Sheet = class {\n  constructor(name, rootNode) {\n    this.name = name;\n    this.rootNode = rootNode;\n    // Hash->css\n    this.storedStyles = {};\n    // styles->hash\n    this.storedClasses = {};\n    this.style = \"\";\n    this.count = 0;\n    this.id = `flairup-${name}`;\n    this.styleTag = this.createStyleTag();\n  }\n  getStyle() {\n    return this.style;\n  }\n  append(css) {\n    this.style = appendString(this.style, css);\n  }\n  apply() {\n    this.count++;\n    if (!this.styleTag) {\n      return;\n    }\n    this.styleTag.innerHTML = this.style;\n  }\n  isApplied() {\n    return !!this.styleTag;\n  }\n  createStyleTag() {\n    if (typeof document === \"undefined\" || this.isApplied() ||\n    // Explicitly disallow mounting to the DOM\n    this.rootNode === null) {\n      return this.styleTag;\n    }\n    const styleTag = document.createElement(\"style\");\n    styleTag.type = \"text/css\";\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n  addRule(rule) {\n    const storedClass = this.storedClasses[rule.key];\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n    this.append(rule.toString());\n    return rule.hash;\n  }\n};\n\n// src/utils/forIn.ts\nfunction forIn(obj, fn) {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n\n// src/cx.ts\nfunction cx(...args) {\n  const classes = args.reduce((classes2, arg) => {\n    if (arg instanceof Set) {\n      classes2.push(...arg);\n    } else if (typeof arg === \"string\") {\n      classes2.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes2.push(cx(...arg));\n    } else if (typeof arg === \"object\") {\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes2.push(key);\n        }\n      });\n    }\n    return classes2;\n  }, []);\n  return joinTruthy(classes, \" \").trim();\n}\n\n// src/index.ts\nfunction createSheet(name, rootNode) {\n  const sheet = new Sheet(name, rootNode);\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet)\n  };\n  function create(styles) {\n    const scopedStyles = {};\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(([scopeName, styles2, selector]) => {\n      iterateStyles(sheet, styles2, selector).forEach(className => {\n        addScopedStyle(scopeName, className);\n      });\n    });\n    sheet.apply();\n    return scopedStyles;\n    function addScopedStyle(name2, className) {\n      scopedStyles[name2] = scopedStyles[name2] ?? /* @__PURE__ */new Set();\n      scopedStyles[name2].add(className);\n    }\n  }\n}\nfunction iteratePreconditions(sheet, styles, selector) {\n  const output = [];\n  forIn(styles, (key, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(sheet, value, selector.addPrecondition(key)).forEach(item => output.push(item));\n    }\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n  return output;\n}\nfunction iterateStyles(sheet, styles, selector) {\n  const output = /* @__PURE__ */new Set();\n  forIn(styles, (property, value) => {\n    let res = [];\n    if (isStyleCondition(property)) {\n      res = iterateStyles(sheet, value, selector.addPostcondition(property));\n    } else if (isDirectClass(property)) {\n      res = asArray(value);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value, property, selector);\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value, selector);\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n    return addEachClass(res, output);\n  });\n  return output;\n}\nfunction addEachClass(list, to) {\n  list.forEach(className => to.add(className));\n  return to;\n}\nfunction cssVariablesBlock(sheet, styles, selector) {\n  const classes = /* @__PURE__ */new Set();\n  const chunkRows = [];\n  forIn(styles, (property, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n  if (chunkRows.length) {\n    const output = chunkRows.join(\" \");\n    sheet.append(`${mergeSelectors(selector.preconditions, {\n      right: selector.scopeClassName\n    })} {${output}}`);\n  }\n  classes.add(selector.scopeClassName);\n  return classes;\n}\nfunction handleMediaQuery(sheet, styles, mediaQuery, selector) {\n  sheet.append(mediaQuery + \" {\");\n  const output = iterateStyles(sheet, styles, selector);\n  sheet.append(\"}\");\n  return output;\n}\nexport { createSheet, cx };", "map": {"version": 3, "names": ["asArray", "v", "concat", "isPsuedoSelector", "selector", "startsWith", "isStyleCondition", "isString", "length", "includes", "slice", "isImmediatePostcondition", "isValidProperty", "property", "value", "isCssVariables", "isMediaQuery", "isDirectClass", "joinTruthy", "arr", "delimiter", "filter", "Boolean", "join", "stableHash", "prefix", "seed", "hash", "toString", "i", "char", "charCodeAt", "handlePropertyValue", "camelCaseToDash", "str", "replace", "toLowerCase", "joinedProperty", "toClass", "appendString", "base", "line", "Rule", "_Rule", "constructor", "sheet", "joined", "joinedConditions", "preconditions", "postconditions", "hasConditions", "scopeClassName", "name", "key", "selectors", "mergeSelectors", "right", "left", "genRule", "transformedProperty", "output", "reduce", "selectors2", "current", "Selector", "_Selector", "scopeName", "setScope", "count", "addScope", "addPrecondition", "precondition", "addPostcondition", "postcondition", "createRule", "Sheet", "rootNode", "storedStyles", "storedClasses", "style", "id", "styleTag", "createStyleTag", "getStyle", "append", "css", "apply", "innerHTML", "isApplied", "document", "createElement", "type", "head", "append<PERSON><PERSON><PERSON>", "addRule", "rule", "storedClass", "forIn", "obj", "fn", "trim", "cx", "args", "classes", "classes2", "arg", "Set", "push", "Array", "isArray", "Object", "entries", "for<PERSON>ach", "createSheet", "create", "bind", "styles", "scopedStyles", "iteratePreconditions", "styles2", "iterateStyles", "className", "addScopedStyle", "name2", "add", "item", "res", "handleMediaQuery", "cssVariablesBlock", "addEachClass", "list", "to", "chunkRows", "mediaQuery"], "sources": ["E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\asArray.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\is.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\joinTruthy.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\stableHash.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\stringManipulators.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\Rule.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\Sheet.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\utils\\forIn.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\cx.ts", "E:\\My MERN Projects\\funnecto\\client\\node_modules\\flairup\\src\\index.ts"], "sourcesContent": ["export function asArray<T>(v: T | T[]): T[] {\n  return [].concat(v as unknown as []);\n}\n", "import { ClassName } from '../types.js';\n\nexport function isPsuedoSelector(selector: string): boolean {\n  return selector.startsWith(':');\n}\n\nexport function isStyleCondition(selector: string): boolean {\n  return (\n    isString(selector) &&\n    (selector === '*' ||\n      (selector.length > 1 && ':>~.+*'.includes(selector.slice(0, 1))) ||\n      isImmediatePostcondition(selector))\n  );\n}\n\nexport function isValidProperty(\n  property: string,\n  value: unknown,\n): value is string {\n  return (\n    (isString(value) || typeof value === 'number') &&\n    !isCssVariables(property) &&\n    !isPsuedoSelector(property) &&\n    !isMediaQuery(property)\n  );\n}\n\nexport function isMediaQuery(selector: string): boolean {\n  return selector.startsWith('@media');\n}\n\nexport function isDirectClass(selector: string): boolean {\n  return selector === '.';\n}\n\nexport function isCssVariables(selector: string): boolean {\n  return selector === '--';\n}\n\nexport function isString(value: unknown): value is string {\n  return value + '' === value;\n}\n\nexport function isClassName(value: unknown): value is ClassName {\n  return isString(value) && value.length > 1 && value.startsWith('.');\n}\n\nexport function isImmediatePostcondition(\n  value: unknown,\n): value is `&${string}` {\n  return isString(value) && (value.startsWith('&') || isPsuedoSelector(value));\n}\n", "export function joinTruthy(arr: unknown[], delimiter: string = ''): string {\n  return arr.filter(Boolean).join(delimiter);\n}\n", "// Stable hash function.\nexport function stableHash(prefix: string, seed: string): string {\n  let hash = 0;\n  if (seed.length === 0) return hash.toString();\n  for (let i = 0; i < seed.length; i++) {\n    const char = seed.charCodeAt(i);\n    hash = (hash << 5) - hash + char;\n    hash = hash & hash; // Convert to 32bit integer\n  }\n  return `${prefix ?? 'cl'}_${hash.toString(36)}`;\n}\n", "// Some properties need special handling\nexport function handlePropertyValue(property: string, value: string): string {\n  if (property === 'content') {\n    return `\"${value}\"`;\n  }\n\n  return value;\n}\n\nexport function camelCaseToDash(str: string): string {\n  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();\n}\n\nexport function joinedProperty(property: string, value: string): string {\n  return `${property}:${value}`;\n}\n\nexport function toClass(str: string): string {\n  return str ? `.${str}` : '';\n}\n\nexport function appendString(base: string, line: string): string {\n  return base ? `${base}\\n${line}` : line;\n}\n", "import { Sheet } from './Sheet';\nimport { asArray } from './utils/asArray';\nimport { isImmediatePostcondition, isPsuedoSelector } from './utils/is';\nimport { joinTruthy } from './utils/joinTruthy';\nimport { stableHash } from './utils/stableHash';\nimport {\n  camelCaseToDash,\n  handlePropertyValue,\n  joinedProperty,\n  toClass,\n} from './utils/stringManipulators';\n\nexport class Rule {\n  public hash: string;\n  public joined: string;\n  public key: string;\n\n  constructor(\n    private sheet: Sheet,\n    public property: string,\n    public value: string,\n    private selector: Selector,\n  ) {\n    this.property = property;\n    this.value = value;\n    this.joined = joinedProperty(property, value);\n    const joinedConditions = this.selector.preconditions.concat(\n      this.selector.postconditions,\n    );\n    this.hash = this.selector.hasConditions\n      ? (this.selector.scopeClassName as string)\n      : stableHash(this.sheet.name, this.joined);\n    this.key = joinTruthy([this.joined, joinedConditions, this.hash]);\n  }\n\n  public toString(): string {\n    let selectors = mergeSelectors(this.selector.preconditions, {\n      right: this.hash,\n    });\n\n    selectors = mergeSelectors(this.selector.postconditions, {\n      left: selectors,\n    });\n\n    return `${selectors} {${Rule.genRule(this.property, this.value)}}`;\n  }\n\n  static genRule(property: string, value: string): string {\n    const transformedProperty = camelCaseToDash(property);\n    return (\n      joinedProperty(\n        transformedProperty,\n        handlePropertyValue(property, value),\n      ) + ';'\n    );\n  }\n}\n\nexport function mergeSelectors(\n  selectors: string[],\n  { left = '', right = '' }: { left?: string; right?: string } = {},\n): string {\n  const output = selectors.reduce((selectors, current) => {\n    if (isPsuedoSelector(current)) {\n      return selectors + current;\n    }\n\n    if (isImmediatePostcondition(current)) {\n      return selectors + current.slice(1);\n    }\n\n    return joinTruthy([selectors, current], ' ');\n\n    // selector then postcondition\n  }, left);\n\n  // preconditions, then selector\n  return joinTruthy([output, toClass(right)], ' ');\n}\n\nexport class Selector {\n  public preconditions: string[] = [];\n  public scopeClassName: string | null = null;\n  public scopeName: string | null = null;\n  public postconditions: string[] = [];\n\n  constructor(\n    private sheet: Sheet,\n    scopeName: string | null = null,\n    {\n      preconditions,\n      postconditions,\n    }: {\n      preconditions?: string[] | string | undefined;\n      postconditions?: string[] | string | undefined;\n    } = {},\n  ) {\n    this.preconditions = preconditions ? asArray(preconditions) : [];\n    this.postconditions = postconditions ? asArray(postconditions) : [];\n    this.setScope(scopeName);\n  }\n\n  private setScope(scopeName: string | null): Selector {\n    if (!scopeName) {\n      return this;\n    }\n\n    if (!this.scopeClassName) {\n      this.scopeName = scopeName;\n      this.scopeClassName = stableHash(\n        this.sheet.name,\n        // adding the count guarantees uniqueness across style.create calls\n        scopeName + this.sheet.count,\n      );\n    }\n\n    return this;\n  }\n\n  get hasConditions(): boolean {\n    return this.preconditions.length > 0 || this.postconditions.length > 0;\n  }\n\n  addScope(scopeName: string): Selector {\n    return new Selector(this.sheet, scopeName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions,\n    });\n  }\n\n  addPrecondition(precondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      postconditions: this.postconditions,\n      preconditions: this.preconditions.concat(precondition),\n    });\n  }\n\n  addPostcondition(postcondition: string): Selector {\n    return new Selector(this.sheet, this.scopeClassName, {\n      preconditions: this.preconditions,\n      postconditions: this.postconditions.concat(postcondition),\n    });\n  }\n\n  createRule(property: string, value: string): Rule {\n    return new Rule(this.sheet, property, value, this);\n  }\n}\n", "import { Rule } from './Rule.js';\nimport { StoredStyles } from './types.js';\nimport { isString } from './utils/is.js';\nimport { appendString } from './utils/stringManipulators.js';\n\nexport class Sheet {\n  private styleTag: HTMLStyleElement | undefined;\n\n  // Hash->css\n  private storedStyles: StoredStyles = {};\n\n  // styles->hash\n  private storedClasses: Record<string, string> = {};\n  private style: string = '';\n  public count = 0;\n  public id: string;\n\n  constructor(\n    public name: string,\n    private rootNode?: HTMLElement | null,\n  ) {\n    this.id = `flairup-${name}`;\n\n    this.styleTag = this.createStyleTag();\n  }\n\n  getStyle(): string {\n    return this.style;\n  }\n\n  append(css: string): void {\n    this.style = appendString(this.style, css);\n  }\n\n  apply(): void {\n    this.count++;\n\n    if (!this.styleTag) {\n      return;\n    }\n\n    this.styleTag.innerHTML = this.style;\n  }\n\n  isApplied(): boolean {\n    return !!this.styleTag;\n  }\n\n  createStyleTag(): HTMLStyleElement | undefined {\n    // check that we're in the browser and have access to the DOM\n    if (\n      typeof document === 'undefined' ||\n      this.isApplied() ||\n      // Explicitly disallow mounting to the DOM\n      this.rootNode === null\n    ) {\n      return this.styleTag;\n    }\n\n    const styleTag = document.createElement('style');\n    styleTag.type = 'text/css';\n    styleTag.id = this.id;\n    (this.rootNode ?? document.head).appendChild(styleTag);\n    return styleTag;\n  }\n\n  addRule(rule: Rule): string {\n    const storedClass = this.storedClasses[rule.key];\n\n    if (isString(storedClass)) {\n      return storedClass;\n    }\n\n    this.storedClasses[rule.key] = rule.hash;\n    this.storedStyles[rule.hash] = [rule.property, rule.value];\n\n    this.append(rule.toString());\n    return rule.hash;\n  }\n}\n", "export function forIn<O extends Record<string, unknown>>(\n  obj: O,\n  fn: (key: string, value: O[string]) => void,\n): void {\n  for (const key in obj) {\n    fn(key.trim(), obj[key]);\n  }\n}\n", "import { joinTruthy } from './utils/joinTruthy';\n\nexport function cx(...args: unknown[]): string {\n  const classes = args.reduce((classes: string[], arg) => {\n    if (arg instanceof Set) {\n      classes.push(...arg);\n    } else if (typeof arg === 'string') {\n      classes.push(arg);\n    } else if (Array.isArray(arg)) {\n      classes.push(cx(...arg));\n    } else if (typeof arg === 'object') {\n      // @ts-expect-error - it is a string\n      Object.entries(arg).forEach(([key, value]) => {\n        if (value) {\n          classes.push(key);\n        }\n      });\n    }\n\n    return classes;\n  }, [] as string[]);\n\n  return joinTruthy(classes, ' ').trim();\n}\n", "import { Rule, Selector, mergeSelectors } from './Rule.js';\nimport { Sheet } from './Sheet.js';\nimport {\n  CSSVariablesObject,\n  ClassSet,\n  CreateSheetInput,\n  DirectClass,\n  ScopedStyles,\n  Styles,\n  createSheetReturn,\n} from './types.js';\nimport { asArray } from './utils/asArray.js';\nimport { forIn } from './utils/forIn.js';\nimport {\n  isCssVariables,\n  isDirectClass,\n  isMediaQuery,\n  isStyleCondition,\n  isValidProperty,\n} from './utils/is.js';\n\nexport { cx } from './cx.js';\n\nexport type { CreateSheetInput, Styles };\n\nexport function createSheet(\n  name: string,\n  rootNode?: HTMLElement | null,\n): createSheetReturn {\n  const sheet = new Sheet(name, rootNode);\n\n  return {\n    create,\n    getStyle: sheet.getStyle.bind(sheet),\n    isApplied: sheet.isApplied.bind(sheet),\n  };\n\n  function create<K extends string>(styles: CreateSheetInput<K>) {\n    const scopedStyles: ScopedStyles<K> = {} as ScopedStyles<K>;\n\n    iteratePreconditions(sheet, styles, new Selector(sheet)).forEach(\n      ([scopeName, styles, selector]) => {\n        iterateStyles(sheet, styles as Styles, selector).forEach(\n          (className) => {\n            addScopedStyle(scopeName as K, className);\n          },\n        );\n      },\n    );\n\n    // Commit the styles to the sheet.\n    // Done only once per create call.\n    // This way we do not update the DOM on every style.\n    sheet.apply();\n\n    return scopedStyles;\n\n    function addScopedStyle(name: K, className: string) {\n      scopedStyles[name as keyof ScopedStyles<K>] =\n        scopedStyles[name as keyof ScopedStyles<K>] ?? new Set<string>();\n      scopedStyles[name as keyof ScopedStyles<K>].add(className);\n    }\n  }\n}\n\n// This one plucks out all of the preconditions\n// and creates selector objects from them\nfunction iteratePreconditions(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n) {\n  const output: Array<[string, Styles, Selector]> = [];\n\n  forIn(styles, (key: string, value) => {\n    if (isStyleCondition(key)) {\n      return iteratePreconditions(\n        sheet,\n        value as Styles,\n        selector.addPrecondition(key),\n      ).forEach((item) => output.push(item));\n    }\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore - this is a valid case\n    output.push([key, styles[key], selector.addScope(key)]);\n  });\n\n  return output;\n}\n\nfunction iterateStyles(\n  sheet: Sheet,\n  styles: Styles,\n  selector: Selector,\n): ClassSet {\n  const output: ClassSet = new Set<string>();\n  // eslint-disable-next-line max-statements\n  forIn(styles, (property, value) => {\n    let res: string[] | Set<string> = [];\n\n    // Postconditions\n    if (isStyleCondition(property)) {\n      res = iterateStyles(\n        sheet,\n        value as Styles,\n        selector.addPostcondition(property),\n      );\n      // Direct classes: \".\": \"className\"\n    } else if (isDirectClass(property)) {\n      res = asArray(value as DirectClass);\n    } else if (isMediaQuery(property)) {\n      res = handleMediaQuery(sheet, value as Styles, property, selector);\n\n      // \"--\": { \"--variable\": \"value\" }\n    } else if (isCssVariables(property)) {\n      res = cssVariablesBlock(sheet, value as CSSVariablesObject, selector);\n\n      // \"property\": \"value\"\n    } else if (isValidProperty(property, value)) {\n      const rule = selector.createRule(property, value);\n      sheet.addRule(rule);\n      output.add(rule.hash);\n    }\n\n    return addEachClass(res, output);\n  });\n\n  return output;\n}\n\nfunction addEachClass(list: string[] | Set<string>, to: Set<string>) {\n  list.forEach((className) => to.add(className));\n  return to;\n}\n\n// eslint-disable-next-line max-statements\nfunction cssVariablesBlock(\n  sheet: Sheet,\n  styles: CSSVariablesObject,\n  selector: Selector,\n) {\n  const classes: ClassSet = new Set<string>();\n\n  const chunkRows: string[] = [];\n  forIn(styles, (property: string, value) => {\n    if (isValidProperty(property, value)) {\n      chunkRows.push(Rule.genRule(property, value));\n      return;\n    }\n    const res = iterateStyles(sheet, value ?? {}, selector);\n    addEachClass(res, classes);\n  });\n\n  if (!selector.scopeClassName) {\n    return classes;\n  }\n\n  if (chunkRows.length) {\n    const output = chunkRows.join(' ');\n    sheet.append(\n      `${mergeSelectors(selector.preconditions, {\n        right: selector.scopeClassName,\n      })} {${output}}`,\n    );\n  }\n\n  classes.add(selector.scopeClassName);\n  return classes;\n}\n\nfunction handleMediaQuery(\n  sheet: Sheet,\n  styles: Styles,\n  mediaQuery: string,\n  selector: Selector,\n) {\n  sheet.append(mediaQuery + ' {');\n\n  // iterateStyles will internally append each rule to the sheet\n  // as needed. All we have to do is just open the block and close it after.\n  const output = iterateStyles(sheet, styles, selector);\n\n  sheet.append('}');\n\n  return output;\n}\n"], "mappings": ";AAAO,SAASA,QAAWC,CAAA,EAAiB;EAC1C,OAAO,EAAC,CAAEC,MAAA,CAAOD,CAAkB;AACrC;;;ACAO,SAASE,iBAAiBC,QAAA,EAA2B;EAC1D,OAAOA,QAAA,CAASC,UAAA,CAAW,GAAG;AAChC;AAEO,SAASC,iBAAiBF,QAAA,EAA2B;EAC1D,OACEG,QAAA,CAASH,QAAQ,MAChBA,QAAA,KAAa,OACXA,QAAA,CAASI,MAAA,GAAS,KAAK,SAASC,QAAA,CAASL,QAAA,CAASM,KAAA,CAAM,GAAG,CAAC,CAAC,KAC9DC,wBAAA,CAAyBP,QAAQ;AAEvC;AAEO,SAASQ,gBACdC,QAAA,EACAC,KAAA,EACiB;EACjB,QACGP,QAAA,CAASO,KAAK,KAAK,OAAOA,KAAA,KAAU,aACrC,CAACC,cAAA,CAAeF,QAAQ,KACxB,CAACV,gBAAA,CAAiBU,QAAQ,KAC1B,CAACG,YAAA,CAAaH,QAAQ;AAE1B;AAEO,SAASG,aAAaZ,QAAA,EAA2B;EACtD,OAAOA,QAAA,CAASC,UAAA,CAAW,QAAQ;AACrC;AAEO,SAASY,cAAcb,QAAA,EAA2B;EACvD,OAAOA,QAAA,KAAa;AACtB;AAEO,SAASW,eAAeX,QAAA,EAA2B;EACxD,OAAOA,QAAA,KAAa;AACtB;AAEO,SAASG,SAASO,KAAA,EAAiC;EACxD,OAAOA,KAAA,GAAQ,OAAOA,KAAA;AACxB;AAMO,SAASH,yBACdG,KAAA,EACuB;EACvB,OAAOP,QAAA,CAASO,KAAK,MAAMA,KAAA,CAAMT,UAAA,CAAW,GAAG,KAAKF,gBAAA,CAAiBW,KAAK;AAC5E;;;ACnDO,SAASI,WAAWC,GAAA,EAAgBC,SAAA,GAAoB,IAAY;EACzE,OAAOD,GAAA,CAAIE,MAAA,CAAOC,OAAO,EAAEC,IAAA,CAAKH,SAAS;AAC3C;;;ACDO,SAASI,WAAWC,MAAA,EAAgBC,IAAA,EAAsB;EAC/D,IAAIC,IAAA,GAAO;EACX,IAAID,IAAA,CAAKlB,MAAA,KAAW,GAAG,OAAOmB,IAAA,CAAKC,QAAA,CAAS;EAC5C,SAASC,CAAA,GAAI,GAAGA,CAAA,GAAIH,IAAA,CAAKlB,MAAA,EAAQqB,CAAA,IAAK;IACpC,MAAMC,IAAA,GAAOJ,IAAA,CAAKK,UAAA,CAAWF,CAAC;IAC9BF,IAAA,IAAQA,IAAA,IAAQ,KAAKA,IAAA,GAAOG,IAAA;IAC5BH,IAAA,GAAOA,IAAA,GAAOA,IAAA;EAChB;EACA,OAAO,GAAGF,MAAA,IAAU,IAAI,IAAIE,IAAA,CAAKC,QAAA,CAAS,EAAE,CAAC;AAC/C;;;ACTO,SAASI,oBAAoBnB,QAAA,EAAkBC,KAAA,EAAuB;EAC3E,IAAID,QAAA,KAAa,WAAW;IAC1B,OAAO,IAAIC,KAAK;EAClB;EAEA,OAAOA,KAAA;AACT;AAEO,SAASmB,gBAAgBC,GAAA,EAAqB;EACnD,OAAOA,GAAA,CAAIC,OAAA,CAAQ,mBAAmB,OAAO,EAAEC,WAAA,CAAY;AAC7D;AAEO,SAASC,eAAexB,QAAA,EAAkBC,KAAA,EAAuB;EACtE,OAAO,GAAGD,QAAQ,IAAIC,KAAK;AAC7B;AAEO,SAASwB,QAAQJ,GAAA,EAAqB;EAC3C,OAAOA,GAAA,GAAM,IAAIA,GAAG,KAAK;AAC3B;AAEO,SAASK,aAAaC,IAAA,EAAcC,IAAA,EAAsB;EAC/D,OAAOD,IAAA,GAAO,GAAGA,IAAI;AAAA,EAAKC,IAAI,KAAKA,IAAA;AACrC;;;ACXO,IAAMC,IAAA,GAAN,MAAMC,KAAA,CAAK;EAKhBC,YACUC,KAAA,EACDhC,QAAA,EACAC,KAAA,EACCV,QAAA,EACR;IAJQ,KAAAyC,KAAA,GAAAA,KAAA;IACD,KAAAhC,QAAA,GAAAA,QAAA;IACA,KAAAC,KAAA,GAAAA,KAAA;IACC,KAAAV,QAAA,GAAAA,QAAA;IAER,KAAKS,QAAA,GAAWA,QAAA;IAChB,KAAKC,KAAA,GAAQA,KAAA;IACb,KAAKgC,MAAA,GAAST,cAAA,CAAexB,QAAA,EAAUC,KAAK;IAC5C,MAAMiC,gBAAA,GAAmB,KAAK3C,QAAA,CAAS4C,aAAA,CAAc9C,MAAA,CACnD,KAAKE,QAAA,CAAS6C,cAChB;IACA,KAAKtB,IAAA,GAAO,KAAKvB,QAAA,CAAS8C,aAAA,GACrB,KAAK9C,QAAA,CAAS+C,cAAA,GACf3B,UAAA,CAAW,KAAKqB,KAAA,CAAMO,IAAA,EAAM,KAAKN,MAAM;IAC3C,KAAKO,GAAA,GAAMnC,UAAA,CAAW,CAAC,KAAK4B,MAAA,EAAQC,gBAAA,EAAkB,KAAKpB,IAAI,CAAC;EAClE;EAEOC,SAAA,EAAmB;IACxB,IAAI0B,SAAA,GAAYC,cAAA,CAAe,KAAKnD,QAAA,CAAS4C,aAAA,EAAe;MAC1DQ,KAAA,EAAO,KAAK7B;IACd,CAAC;IAED2B,SAAA,GAAYC,cAAA,CAAe,KAAKnD,QAAA,CAAS6C,cAAA,EAAgB;MACvDQ,IAAA,EAAMH;IACR,CAAC;IAED,OAAO,GAAGA,SAAS,KAAKX,KAAA,CAAKe,OAAA,CAAQ,KAAK7C,QAAA,EAAU,KAAKC,KAAK,CAAC;EACjE;EAEA,OAAO4C,QAAQ7C,QAAA,EAAkBC,KAAA,EAAuB;IACtD,MAAM6C,mBAAA,GAAsB1B,eAAA,CAAgBpB,QAAQ;IACpD,OACEwB,cAAA,CACEsB,mBAAA,EACA3B,mBAAA,CAAoBnB,QAAA,EAAUC,KAAK,CACrC,IAAI;EAER;AACF;AAEO,SAASyC,eACdD,SAAA,EACA;EAAEG,IAAA,GAAO;EAAID,KAAA,GAAQ;AAAG,IAAuC,CAAC,GACxD;EACR,MAAMI,MAAA,GAASN,SAAA,CAAUO,MAAA,CAAO,CAACC,UAAA,EAAWC,OAAA,KAAY;IACtD,IAAI5D,gBAAA,CAAiB4D,OAAO,GAAG;MAC7B,OAAOD,UAAA,GAAYC,OAAA;IACrB;IAEA,IAAIpD,wBAAA,CAAyBoD,OAAO,GAAG;MACrC,OAAOD,UAAA,GAAYC,OAAA,CAAQrD,KAAA,CAAM,CAAC;IACpC;IAEA,OAAOQ,UAAA,CAAW,CAAC4C,UAAA,EAAWC,OAAO,GAAG,GAAG;EAG7C,GAAGN,IAAI;EAGP,OAAOvC,UAAA,CAAW,CAAC0C,MAAA,EAAQtB,OAAA,CAAQkB,KAAK,CAAC,GAAG,GAAG;AACjD;AAEO,IAAMQ,QAAA,GAAN,MAAMC,SAAA,CAAS;EAMpBrB,YACUC,KAAA,EACRqB,SAAA,GAA2B,MAC3B;IACElB,aAAA;IACAC;EACF,IAGI,CAAC,GACL;IATQ,KAAAJ,KAAA,GAAAA,KAAA;IANV,KAAOG,aAAA,GAA0B,EAAC;IAClC,KAAOG,cAAA,GAAgC;IACvC,KAAOe,SAAA,GAA2B;IAClC,KAAOjB,cAAA,GAA2B,EAAC;IAajC,KAAKD,aAAA,GAAgBA,aAAA,GAAgBhD,OAAA,CAAQgD,aAAa,IAAI,EAAC;IAC/D,KAAKC,cAAA,GAAiBA,cAAA,GAAiBjD,OAAA,CAAQiD,cAAc,IAAI,EAAC;IAClE,KAAKkB,QAAA,CAASD,SAAS;EACzB;EAEQC,SAASD,SAAA,EAAoC;IACnD,IAAI,CAACA,SAAA,EAAW;MACd,OAAO;IACT;IAEA,IAAI,CAAC,KAAKf,cAAA,EAAgB;MACxB,KAAKe,SAAA,GAAYA,SAAA;MACjB,KAAKf,cAAA,GAAiB3B,UAAA,CACpB,KAAKqB,KAAA,CAAMO,IAAA;MAAA;MAEXc,SAAA,GAAY,KAAKrB,KAAA,CAAMuB,KACzB;IACF;IAEA,OAAO;EACT;EAEA,IAAIlB,cAAA,EAAyB;IAC3B,OAAO,KAAKF,aAAA,CAAcxC,MAAA,GAAS,KAAK,KAAKyC,cAAA,CAAezC,MAAA,GAAS;EACvE;EAEA6D,SAASH,SAAA,EAA6B;IACpC,OAAO,IAAID,SAAA,CAAS,KAAKpB,KAAA,EAAOqB,SAAA,EAAW;MACzClB,aAAA,EAAe,KAAKA,aAAA;MACpBC,cAAA,EAAgB,KAAKA;IACvB,CAAC;EACH;EAEAqB,gBAAgBC,YAAA,EAAgC;IAC9C,OAAO,IAAIN,SAAA,CAAS,KAAKpB,KAAA,EAAO,KAAKM,cAAA,EAAgB;MACnDF,cAAA,EAAgB,KAAKA,cAAA;MACrBD,aAAA,EAAe,KAAKA,aAAA,CAAc9C,MAAA,CAAOqE,YAAY;IACvD,CAAC;EACH;EAEAC,iBAAiBC,aAAA,EAAiC;IAChD,OAAO,IAAIR,SAAA,CAAS,KAAKpB,KAAA,EAAO,KAAKM,cAAA,EAAgB;MACnDH,aAAA,EAAe,KAAKA,aAAA;MACpBC,cAAA,EAAgB,KAAKA,cAAA,CAAe/C,MAAA,CAAOuE,aAAa;IAC1D,CAAC;EACH;EAEAC,WAAW7D,QAAA,EAAkBC,KAAA,EAAqB;IAChD,OAAO,IAAI4B,IAAA,CAAK,KAAKG,KAAA,EAAOhC,QAAA,EAAUC,KAAA,EAAO,IAAI;EACnD;AACF;;;AC9IO,IAAM6D,KAAA,GAAN,MAAY;EAYjB/B,YACSQ,IAAA,EACCwB,QAAA,EACR;IAFO,KAAAxB,IAAA,GAAAA,IAAA;IACC,KAAAwB,QAAA,GAAAA,QAAA;IAVV;IAAA,KAAQC,YAAA,GAA6B,CAAC;IAGtC;IAAA,KAAQC,aAAA,GAAwC,CAAC;IACjD,KAAQC,KAAA,GAAgB;IACxB,KAAOX,KAAA,GAAQ;IAOb,KAAKY,EAAA,GAAK,WAAW5B,IAAI;IAEzB,KAAK6B,QAAA,GAAW,KAAKC,cAAA,CAAe;EACtC;EAEAC,SAAA,EAAmB;IACjB,OAAO,KAAKJ,KAAA;EACd;EAEAK,OAAOC,GAAA,EAAmB;IACxB,KAAKN,KAAA,GAAQxC,YAAA,CAAa,KAAKwC,KAAA,EAAOM,GAAG;EAC3C;EAEAC,MAAA,EAAc;IACZ,KAAKlB,KAAA;IAEL,IAAI,CAAC,KAAKa,QAAA,EAAU;MAClB;IACF;IAEA,KAAKA,QAAA,CAASM,SAAA,GAAY,KAAKR,KAAA;EACjC;EAEAS,UAAA,EAAqB;IACnB,OAAO,CAAC,CAAC,KAAKP,QAAA;EAChB;EAEAC,eAAA,EAA+C;IAE7C,IACE,OAAOO,QAAA,KAAa,eACpB,KAAKD,SAAA,CAAU;IAAA;IAEf,KAAKZ,QAAA,KAAa,MAClB;MACA,OAAO,KAAKK,QAAA;IACd;IAEA,MAAMA,QAAA,GAAWQ,QAAA,CAASC,aAAA,CAAc,OAAO;IAC/CT,QAAA,CAASU,IAAA,GAAO;IAChBV,QAAA,CAASD,EAAA,GAAK,KAAKA,EAAA;IACnB,CAAC,KAAKJ,QAAA,IAAYa,QAAA,CAASG,IAAA,EAAMC,WAAA,CAAYZ,QAAQ;IACrD,OAAOA,QAAA;EACT;EAEAa,QAAQC,IAAA,EAAoB;IAC1B,MAAMC,WAAA,GAAc,KAAKlB,aAAA,CAAciB,IAAA,CAAK1C,GAAG;IAE/C,IAAI9C,QAAA,CAASyF,WAAW,GAAG;MACzB,OAAOA,WAAA;IACT;IAEA,KAAKlB,aAAA,CAAciB,IAAA,CAAK1C,GAAG,IAAI0C,IAAA,CAAKpE,IAAA;IACpC,KAAKkD,YAAA,CAAakB,IAAA,CAAKpE,IAAI,IAAI,CAACoE,IAAA,CAAKlF,QAAA,EAAUkF,IAAA,CAAKjF,KAAK;IAEzD,KAAKsE,MAAA,CAAOW,IAAA,CAAKnE,QAAA,CAAS,CAAC;IAC3B,OAAOmE,IAAA,CAAKpE,IAAA;EACd;AACF;;;AC/EO,SAASsE,MACdC,GAAA,EACAC,EAAA,EACM;EACN,WAAW9C,GAAA,IAAO6C,GAAA,EAAK;IACrBC,EAAA,CAAG9C,GAAA,CAAI+C,IAAA,CAAK,GAAGF,GAAA,CAAI7C,GAAG,CAAC;EACzB;AACF;;;ACLO,SAASgD,GAAA,GAAMC,IAAA,EAAyB;EAC7C,MAAMC,OAAA,GAAUD,IAAA,CAAKzC,MAAA,CAAO,CAAC2C,QAAA,EAAmBC,GAAA,KAAQ;IACtD,IAAIA,GAAA,YAAeC,GAAA,EAAK;MACtBF,QAAA,CAAQG,IAAA,CAAK,GAAGF,GAAG;IACrB,WAAW,OAAOA,GAAA,KAAQ,UAAU;MAClCD,QAAA,CAAQG,IAAA,CAAKF,GAAG;IAClB,WAAWG,KAAA,CAAMC,OAAA,CAAQJ,GAAG,GAAG;MAC7BD,QAAA,CAAQG,IAAA,CAAKN,EAAA,CAAG,GAAGI,GAAG,CAAC;IACzB,WAAW,OAAOA,GAAA,KAAQ,UAAU;MAElCK,MAAA,CAAOC,OAAA,CAAQN,GAAG,EAAEO,OAAA,CAAQ,CAAC,CAAC3D,GAAA,EAAKvC,KAAK,MAAM;QAC5C,IAAIA,KAAA,EAAO;UACT0F,QAAA,CAAQG,IAAA,CAAKtD,GAAG;QAClB;MACF,CAAC;IACH;IAEA,OAAOmD,QAAA;EACT,GAAG,EAAc;EAEjB,OAAOtF,UAAA,CAAWqF,OAAA,EAAS,GAAG,EAAEH,IAAA,CAAK;AACvC;;;ACEO,SAASa,YACd7D,IAAA,EACAwB,QAAA,EACmB;EACnB,MAAM/B,KAAA,GAAQ,IAAI8B,KAAA,CAAMvB,IAAA,EAAMwB,QAAQ;EAEtC,OAAO;IACLsC,MAAA;IACA/B,QAAA,EAAUtC,KAAA,CAAMsC,QAAA,CAASgC,IAAA,CAAKtE,KAAK;IACnC2C,SAAA,EAAW3C,KAAA,CAAM2C,SAAA,CAAU2B,IAAA,CAAKtE,KAAK;EACvC;EAEA,SAASqE,OAAyBE,MAAA,EAA6B;IAC7D,MAAMC,YAAA,GAAgC,CAAC;IAEvCC,oBAAA,CAAqBzE,KAAA,EAAOuE,MAAA,EAAQ,IAAIpD,QAAA,CAASnB,KAAK,CAAC,EAAEmE,OAAA,CACvD,CAAC,CAAC9C,SAAA,EAAWqD,OAAA,EAAQnH,QAAQ,MAAM;MACjCoH,aAAA,CAAc3E,KAAA,EAAO0E,OAAA,EAAkBnH,QAAQ,EAAE4G,OAAA,CAC9CS,SAAA,IAAc;QACbC,cAAA,CAAexD,SAAA,EAAgBuD,SAAS;MAC1C,CACF;IACF,CACF;IAKA5E,KAAA,CAAMyC,KAAA,CAAM;IAEZ,OAAO+B,YAAA;IAEP,SAASK,eAAeC,KAAA,EAASF,SAAA,EAAmB;MAClDJ,YAAA,CAAaM,KAA6B,IACxCN,YAAA,CAAaM,KAA6B,KAAK,mBAAIjB,GAAA,CAAY;MACjEW,YAAA,CAAaM,KAA6B,EAAEC,GAAA,CAAIH,SAAS;IAC3D;EACF;AACF;AAIA,SAASH,qBACPzE,KAAA,EACAuE,MAAA,EACAhH,QAAA,EACA;EACA,MAAMwD,MAAA,GAA4C,EAAC;EAEnDqC,KAAA,CAAMmB,MAAA,EAAQ,CAAC/D,GAAA,EAAavC,KAAA,KAAU;IACpC,IAAIR,gBAAA,CAAiB+C,GAAG,GAAG;MACzB,OAAOiE,oBAAA,CACLzE,KAAA,EACA/B,KAAA,EACAV,QAAA,CAASkE,eAAA,CAAgBjB,GAAG,CAC9B,EAAE2D,OAAA,CAASa,IAAA,IAASjE,MAAA,CAAO+C,IAAA,CAAKkB,IAAI,CAAC;IACvC;IAIAjE,MAAA,CAAO+C,IAAA,CAAK,CAACtD,GAAA,EAAK+D,MAAA,CAAO/D,GAAG,GAAGjD,QAAA,CAASiE,QAAA,CAAShB,GAAG,CAAC,CAAC;EACxD,CAAC;EAED,OAAOO,MAAA;AACT;AAEA,SAAS4D,cACP3E,KAAA,EACAuE,MAAA,EACAhH,QAAA,EACU;EACV,MAAMwD,MAAA,GAAmB,mBAAI8C,GAAA,CAAY;EAEzCT,KAAA,CAAMmB,MAAA,EAAQ,CAACvG,QAAA,EAAUC,KAAA,KAAU;IACjC,IAAIgH,GAAA,GAA8B,EAAC;IAGnC,IAAIxH,gBAAA,CAAiBO,QAAQ,GAAG;MAC9BiH,GAAA,GAAMN,aAAA,CACJ3E,KAAA,EACA/B,KAAA,EACAV,QAAA,CAASoE,gBAAA,CAAiB3D,QAAQ,CACpC;IAEF,WAAWI,aAAA,CAAcJ,QAAQ,GAAG;MAClCiH,GAAA,GAAM9H,OAAA,CAAQc,KAAoB;IACpC,WAAWE,YAAA,CAAaH,QAAQ,GAAG;MACjCiH,GAAA,GAAMC,gBAAA,CAAiBlF,KAAA,EAAO/B,KAAA,EAAiBD,QAAA,EAAUT,QAAQ;IAGnE,WAAWW,cAAA,CAAeF,QAAQ,GAAG;MACnCiH,GAAA,GAAME,iBAAA,CAAkBnF,KAAA,EAAO/B,KAAA,EAA6BV,QAAQ;IAGtE,WAAWQ,eAAA,CAAgBC,QAAA,EAAUC,KAAK,GAAG;MAC3C,MAAMiF,IAAA,GAAO3F,QAAA,CAASsE,UAAA,CAAW7D,QAAA,EAAUC,KAAK;MAChD+B,KAAA,CAAMiD,OAAA,CAAQC,IAAI;MAClBnC,MAAA,CAAOgE,GAAA,CAAI7B,IAAA,CAAKpE,IAAI;IACtB;IAEA,OAAOsG,YAAA,CAAaH,GAAA,EAAKlE,MAAM;EACjC,CAAC;EAED,OAAOA,MAAA;AACT;AAEA,SAASqE,aAAaC,IAAA,EAA8BC,EAAA,EAAiB;EACnED,IAAA,CAAKlB,OAAA,CAASS,SAAA,IAAcU,EAAA,CAAGP,GAAA,CAAIH,SAAS,CAAC;EAC7C,OAAOU,EAAA;AACT;AAGA,SAASH,kBACPnF,KAAA,EACAuE,MAAA,EACAhH,QAAA,EACA;EACA,MAAMmG,OAAA,GAAoB,mBAAIG,GAAA,CAAY;EAE1C,MAAM0B,SAAA,GAAsB,EAAC;EAC7BnC,KAAA,CAAMmB,MAAA,EAAQ,CAACvG,QAAA,EAAkBC,KAAA,KAAU;IACzC,IAAIF,eAAA,CAAgBC,QAAA,EAAUC,KAAK,GAAG;MACpCsH,SAAA,CAAUzB,IAAA,CAAKjE,IAAA,CAAKgB,OAAA,CAAQ7C,QAAA,EAAUC,KAAK,CAAC;MAC5C;IACF;IACA,MAAMgH,GAAA,GAAMN,aAAA,CAAc3E,KAAA,EAAO/B,KAAA,IAAS,CAAC,GAAGV,QAAQ;IACtD6H,YAAA,CAAaH,GAAA,EAAKvB,OAAO;EAC3B,CAAC;EAED,IAAI,CAACnG,QAAA,CAAS+C,cAAA,EAAgB;IAC5B,OAAOoD,OAAA;EACT;EAEA,IAAI6B,SAAA,CAAU5H,MAAA,EAAQ;IACpB,MAAMoD,MAAA,GAASwE,SAAA,CAAU7G,IAAA,CAAK,GAAG;IACjCsB,KAAA,CAAMuC,MAAA,CACJ,GAAG7B,cAAA,CAAenD,QAAA,CAAS4C,aAAA,EAAe;MACxCQ,KAAA,EAAOpD,QAAA,CAAS+C;IAClB,CAAC,CAAC,KAAKS,MAAM,GACf;EACF;EAEA2C,OAAA,CAAQqB,GAAA,CAAIxH,QAAA,CAAS+C,cAAc;EACnC,OAAOoD,OAAA;AACT;AAEA,SAASwB,iBACPlF,KAAA,EACAuE,MAAA,EACAiB,UAAA,EACAjI,QAAA,EACA;EACAyC,KAAA,CAAMuC,MAAA,CAAOiD,UAAA,GAAa,IAAI;EAI9B,MAAMzE,MAAA,GAAS4D,aAAA,CAAc3E,KAAA,EAAOuE,MAAA,EAAQhH,QAAQ;EAEpDyC,KAAA,CAAMuC,MAAA,CAAO,GAAG;EAEhB,OAAOxB,MAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}