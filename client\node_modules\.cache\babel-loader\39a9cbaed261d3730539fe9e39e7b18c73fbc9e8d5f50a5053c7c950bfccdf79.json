{"ast": null, "code": "export default function prepareTypographyVars(typography) {\n  const vars = {};\n  const entries = Object.entries(typography);\n  entries.forEach(entry => {\n    const [key, value] = entry;\n    if (typeof value === 'object') {\n      vars[key] = `${value.fontStyle ? `${value.fontStyle} ` : ''}${value.fontVariant ? `${value.fontVariant} ` : ''}${value.fontWeight ? `${value.fontWeight} ` : ''}${value.fontStretch ? `${value.fontStretch} ` : ''}${value.fontSize || ''}${value.lineHeight ? `/${value.lineHeight} ` : ''}${value.fontFamily || ''}`;\n    }\n  });\n  return vars;\n}", "map": {"version": 3, "names": ["prepareTypographyVars", "typography", "vars", "entries", "Object", "for<PERSON>ach", "entry", "key", "value", "fontStyle", "fontVariant", "fontWeight", "fontStretch", "fontSize", "lineHeight", "fontFamily"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/cssVars/prepareTypographyVars.js"], "sourcesContent": ["export default function prepareTypographyVars(typography) {\n  const vars = {};\n  const entries = Object.entries(typography);\n  entries.forEach(entry => {\n    const [key, value] = entry;\n    if (typeof value === 'object') {\n      vars[key] = `${value.fontStyle ? `${value.fontStyle} ` : ''}${value.fontVariant ? `${value.fontVariant} ` : ''}${value.fontWeight ? `${value.fontWeight} ` : ''}${value.fontStretch ? `${value.fontStretch} ` : ''}${value.fontSize || ''}${value.lineHeight ? `/${value.lineHeight} ` : ''}${value.fontFamily || ''}`;\n    }\n  });\n  return vars;\n}"], "mappings": "AAAA,eAAe,SAASA,qBAAqBA,CAACC,UAAU,EAAE;EACxD,MAAMC,IAAI,GAAG,CAAC,CAAC;EACf,MAAMC,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACF,UAAU,CAAC;EAC1CE,OAAO,CAACE,OAAO,CAACC,KAAK,IAAI;IACvB,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,GAAGF,KAAK;IAC1B,IAAI,OAAOE,KAAK,KAAK,QAAQ,EAAE;MAC7BN,IAAI,CAACK,GAAG,CAAC,GAAG,GAAGC,KAAK,CAACC,SAAS,GAAG,GAAGD,KAAK,CAACC,SAAS,GAAG,GAAG,EAAE,GAAGD,KAAK,CAACE,WAAW,GAAG,GAAGF,KAAK,CAACE,WAAW,GAAG,GAAG,EAAE,GAAGF,KAAK,CAACG,UAAU,GAAG,GAAGH,KAAK,CAACG,UAAU,GAAG,GAAG,EAAE,GAAGH,KAAK,CAACI,WAAW,GAAG,GAAGJ,KAAK,CAACI,WAAW,GAAG,GAAG,EAAE,GAAGJ,KAAK,CAACK,QAAQ,IAAI,EAAE,GAAGL,KAAK,CAACM,UAAU,GAAG,IAAIN,KAAK,CAACM,UAAU,GAAG,GAAG,EAAE,GAAGN,KAAK,CAACO,UAAU,IAAI,EAAE,EAAE;IACxT;EACF,CAAC,CAAC;EACF,OAAOb,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}