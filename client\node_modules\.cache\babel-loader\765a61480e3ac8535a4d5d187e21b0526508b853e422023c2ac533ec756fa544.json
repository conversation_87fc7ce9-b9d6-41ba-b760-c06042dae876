{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link differenceInHours} function options.\n */\n\n/**\n * @name differenceInHours\n * @category Hour Helpers\n * @summary Get the number of hours between the given dates.\n *\n * @description\n * Get the number of hours between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of hours\n *\n * @example\n * // How many hours are between 2 July 2014 06:50:00 and 2 July 2014 19:00:00?\n * const result = differenceInHours(\n *   new Date(2014, 6, 2, 19, 0),\n *   new Date(2014, 6, 2, 6, 50)\n * )\n * //=> 12\n */\nexport function differenceInHours(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const diff = (+laterDate_ - +earlierDate_) / millisecondsInHour;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInHours;", "map": {"version": 3, "names": ["getRoundingMethod", "normalizeDates", "millisecondsInHour", "differenceInHours", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "diff", "roundingMethod"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/differenceInHours.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link differenceInHours} function options.\n */\n\n/**\n * @name differenceInHours\n * @category Hour Helpers\n * @summary Get the number of hours between the given dates.\n *\n * @description\n * Get the number of hours between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options.\n *\n * @returns The number of hours\n *\n * @example\n * // How many hours are between 2 July 2014 06:50:00 and 2 July 2014 19:00:00?\n * const result = differenceInHours(\n *   new Date(2014, 6, 2, 19, 0),\n *   new Date(2014, 6, 2, 6, 50)\n * )\n * //=> 12\n */\nexport function differenceInHours(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  const diff = (+laterDate_ - +earlierDate_) / millisecondsInHour;\n  return getRoundingMethod(options?.roundingMethod)(diff);\n}\n\n// Fallback for modularized imports:\nexport default differenceInHours;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,kBAAkB,QAAQ,gBAAgB;;AAEnD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EACjE,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGP,cAAc,CAC/CK,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,MAAMK,IAAI,GAAG,CAAC,CAACH,UAAU,GAAG,CAACC,YAAY,IAAIN,kBAAkB;EAC/D,OAAOF,iBAAiB,CAACM,OAAO,EAAEK,cAAc,CAAC,CAACD,IAAI,CAAC;AACzD;;AAEA;AACA,eAAeP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}