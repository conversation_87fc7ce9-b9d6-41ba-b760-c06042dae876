{"ast": null, "code": "import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addMinutes } from \"./addMinutes.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * The {@link eachMinuteOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport function eachMinuteOfInterval(interval, options) {\n  const {\n    start,\n    end\n  } = normalizeInterval(options?.in, interval);\n  // Set to the start of the minute\n  start.setSeconds(0, 0);\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  let date = reversed ? end : start;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addMinutes(date, step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMinuteOfInterval;", "map": {"version": 3, "names": ["normalizeInterval", "addMinutes", "constructFrom", "eachMinuteOfInterval", "interval", "options", "start", "end", "in", "setSeconds", "reversed", "endTime", "date", "step", "dates", "push", "reverse"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/eachMinuteOfInterval.js"], "sourcesContent": ["import { normalizeInterval } from \"./_lib/normalizeInterval.js\";\nimport { addMinutes } from \"./addMinutes.js\";\nimport { constructFrom } from \"./constructFrom.js\";\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * The {@link eachMinuteOfInterval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam IntervalType - Interval type.\n * @typeParam Options - Options type.\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nexport function eachMinuteOfInterval(interval, options) {\n  const { start, end } = normalizeInterval(options?.in, interval);\n  // Set to the start of the minute\n  start.setSeconds(0, 0);\n\n  let reversed = +start > +end;\n  const endTime = reversed ? +start : +end;\n  let date = reversed ? end : start;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+date <= endTime) {\n    dates.push(constructFrom(start, date));\n    date = addMinutes(date, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachMinuteOfInterval;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,aAAa,QAAQ,oBAAoB;;AAElD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACtD,MAAM;IAAEC,KAAK;IAAEC;EAAI,CAAC,GAAGP,iBAAiB,CAACK,OAAO,EAAEG,EAAE,EAAEJ,QAAQ,CAAC;EAC/D;EACAE,KAAK,CAACG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;EAEtB,IAAIC,QAAQ,GAAG,CAACJ,KAAK,GAAG,CAACC,GAAG;EAC5B,MAAMI,OAAO,GAAGD,QAAQ,GAAG,CAACJ,KAAK,GAAG,CAACC,GAAG;EACxC,IAAIK,IAAI,GAAGF,QAAQ,GAAGH,GAAG,GAAGD,KAAK;EAEjC,IAAIO,IAAI,GAAGR,OAAO,EAAEQ,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZH,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMI,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,IAAI,IAAID,OAAO,EAAE;IACvBG,KAAK,CAACC,IAAI,CAACb,aAAa,CAACI,KAAK,EAAEM,IAAI,CAAC,CAAC;IACtCA,IAAI,GAAGX,UAAU,CAACW,IAAI,EAAEC,IAAI,CAAC;EAC/B;EAEA,OAAOH,QAAQ,GAAGI,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAeX,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}