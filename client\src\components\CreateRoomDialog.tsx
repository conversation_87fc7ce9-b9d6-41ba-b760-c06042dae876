import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Switch,
  Box,
  Typography,
  Slider,
  Alert,
  CircularProgress,
} from '@mui/material';
import { use<PERSON><PERSON>, Controller, SubmitHandler } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import toast from 'react-hot-toast';

import { useChatStore } from '../context/store';
import { apiService } from '../services/api';
import { RoomCreate } from '../types';

interface CreateRoomDialogProps {
  open: boolean;
  onClose: () => void;
}

const schema = yup.object().shape({
  name: yup
    .string()
    .required('Room name is required')
    .min(1, 'Room name cannot be empty')
    .max(50, 'Room name cannot exceed 50 characters'),
  description: yup
    .string()
    .max(200, 'Description cannot exceed 200 characters')
    .optional(),
  type: yup
    .string()
    .oneOf(['public', 'private'] as const)
    .required(),
  settings: yup.object().shape({
    isPublic: yup.boolean().required(),
    allowInvites: yup.boolean().required(),
    maxMembers: yup.number().min(2).max(1000).required(),
  }).required(),
});

interface FormData {
  name: string;
  description?: string;
  type: 'public' | 'private';
  settings: {
    isPublic: boolean;
    allowInvites: boolean;
    maxMembers: number;
  };
}

const CreateRoomDialog: React.FC<CreateRoomDialogProps> = ({ open, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { addRoom, loadUserRooms } = useChatStore();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      name: '',
      description: '',
      type: 'public' as const,
      settings: {
        isPublic: true,
        allowInvites: true,
        maxMembers: 100,
      },
    },
  });

  const watchType = watch('type');
  const watchIsPublic = watch('settings.isPublic');

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    setIsLoading(true);
    setError(null);

    // Basic validation
    if (!data.name.trim()) {
      setError('Room name is required');
      setIsLoading(false);
      return;
    }

    try {
      const roomData: RoomCreate = {
        name: data.name.trim(),
        description: data.description?.trim() || '',
        type: data.type,
        settings: {
          isPublic: data.type === 'public' ? true : data.settings.isPublic,
          allowInvites: data.settings.allowInvites,
          maxMembers: data.settings.maxMembers,
        },
      };

      const newRoom = await apiService.createRoom(roomData);
      addRoom(newRoom);

      toast.success(`Room "${newRoom.name}" created successfully!`);
      handleClose();
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Failed to create room';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 },
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          Create New Room
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Set up a new chat room for your team
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent dividers>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Room Name */}
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Room Name"
                placeholder="Enter room name"
                error={!!errors.name}
                helperText={errors.name?.message || 'Required'}
                margin="normal"
                disabled={isLoading}
                autoFocus
                required
              />
            )}
          />

          {/* Description */}
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Description (Optional)"
                placeholder="Describe what this room is for"
                multiline
                rows={3}
                error={!!errors.description}
                helperText={errors.description?.message}
                margin="normal"
                disabled={isLoading}
              />
            )}
          />

          {/* Room Type */}
          <FormControl component="fieldset" margin="normal" disabled={isLoading}>
            <FormLabel component="legend">Room Type</FormLabel>
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <RadioGroup {...field} row>
                  <FormControlLabel
                    value="public"
                    control={<Radio />}
                    label={
                      <Box>
                        <Typography variant="body2">Public</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Anyone can discover and join
                        </Typography>
                      </Box>
                    }
                  />
                  <FormControlLabel
                    value="private"
                    control={<Radio />}
                    label={
                      <Box>
                        <Typography variant="body2">Private</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Invite-only access
                        </Typography>
                      </Box>
                    }
                  />
                </RadioGroup>
              )}
            />
          </FormControl>

          {/* Advanced Settings */}
          <Typography variant="subtitle2" sx={{ mt: 3, mb: 2 }}>
            Advanced Settings
          </Typography>

          {/* Public Visibility (for private rooms) */}
          {watchType === 'private' && (
            <Box sx={{ mb: 2 }}>
              <Controller
                name="settings.isPublic"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                        disabled={isLoading}
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2">Discoverable</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Allow others to find this room in search
                        </Typography>
                      </Box>
                    }
                  />
                )}
              />
            </Box>
          )}

          {/* Allow Invites */}
          <Box sx={{ mb: 2 }}>
            <Controller
              name="settings.allowInvites"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Switch
                      checked={field.value}
                      onChange={field.onChange}
                      disabled={isLoading}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2">Allow Member Invites</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Let members invite others to this room
                      </Typography>
                    </Box>
                  }
                />
              )}
            />
          </Box>

          {/* Max Members */}
          <Box sx={{ mb: 2 }}>
            <Typography variant="body2" gutterBottom>
              Maximum Members
            </Typography>
            <Controller
              name="settings.maxMembers"
              control={control}
              render={({ field }) => (
                <Box sx={{ px: 2 }}>
                  <Slider
                    {...field}
                    min={2}
                    max={1000}
                    step={1}
                    marks={[
                      { value: 2, label: '2' },
                      { value: 50, label: '50' },
                      { value: 100, label: '100' },
                      { value: 500, label: '500' },
                      { value: 1000, label: '1000' },
                    ]}
                    valueLabelDisplay="auto"
                    disabled={isLoading}
                  />
                  <Typography variant="caption" color="text.secondary">
                    Current limit: {field.value} members
                  </Typography>
                </Box>
              )}
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={16} /> : null}
          >
            {isLoading ? 'Creating...' : 'Create Room'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default CreateRoomDialog;
