[{"E:\\My MERN Projects\\funnecto\\client\\src\\index.tsx": "1", "E:\\My MERN Projects\\funnecto\\client\\src\\reportWebVitals.ts": "2", "E:\\My MERN Projects\\funnecto\\client\\src\\App.tsx": "3", "E:\\My MERN Projects\\funnecto\\client\\src\\context\\store.ts": "4", "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\RegisterPage.tsx": "5", "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\LoginPage.tsx": "6", "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\ChatPage.tsx": "7", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\LoadingScreen.tsx": "8", "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\index.ts": "9", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\Sidebar.tsx": "10", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\UserList.tsx": "11", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\ChatArea.tsx": "12", "E:\\My MERN Projects\\funnecto\\client\\src\\services\\socket.ts": "13", "E:\\My MERN Projects\\funnecto\\client\\src\\services\\api.ts": "14", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\NotificationSystem.tsx": "15", "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\useTyping.ts": "16", "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\useSocket.ts": "17", "E:\\My MERN Projects\\funnecto\\client\\src\\utils\\index.ts": "18", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\UserSearchDialog.tsx": "19", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\MessageInput.tsx": "20", "E:\\My MERN Projects\\funnecto\\client\\src\\components\\CreateRoomDialog.tsx": "21"}, {"size": 554, "mtime": 1754127226219, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1754127225615, "results": "24", "hashOfConfig": "23"}, {"size": 3189, "mtime": 1754128837855, "results": "25", "hashOfConfig": "23"}, {"size": 11124, "mtime": 1754381103190, "results": "26", "hashOfConfig": "23"}, {"size": 6475, "mtime": 1754378958948, "results": "27", "hashOfConfig": "23"}, {"size": 5090, "mtime": 1754378902060, "results": "28", "hashOfConfig": "23"}, {"size": 6900, "mtime": 1754136265031, "results": "29", "hashOfConfig": "23"}, {"size": 766, "mtime": 1754128851659, "results": "30", "hashOfConfig": "23"}, {"size": 99, "mtime": 1754128818010, "results": "31", "hashOfConfig": "23"}, {"size": 10458, "mtime": 1754136466787, "results": "32", "hashOfConfig": "23"}, {"size": 7379, "mtime": 1754129547113, "results": "33", "hashOfConfig": "23"}, {"size": 7895, "mtime": 1754136179791, "results": "34", "hashOfConfig": "23"}, {"size": 6553, "mtime": 1754128698262, "results": "35", "hashOfConfig": "23"}, {"size": 7952, "mtime": 1754128666892, "results": "36", "hashOfConfig": "23"}, {"size": 9902, "mtime": 1754136233736, "results": "37", "hashOfConfig": "23"}, {"size": 1803, "mtime": 1754129559070, "results": "38", "hashOfConfig": "23"}, {"size": 2656, "mtime": 1754128797404, "results": "39", "hashOfConfig": "23"}, {"size": 7786, "mtime": 1754128778685, "results": "40", "hashOfConfig": "23"}, {"size": 10291, "mtime": 1754136415888, "results": "41", "hashOfConfig": "23"}, {"size": 8585, "mtime": 1754136076813, "results": "42", "hashOfConfig": "23"}, {"size": 10197, "mtime": 1754136672080, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sc4zo6", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\My MERN Projects\\funnecto\\client\\src\\index.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\reportWebVitals.ts", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\App.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\context\\store.ts", ["107"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\RegisterPage.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\LoginPage.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\pages\\ChatPage.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\LoadingScreen.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\index.ts", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\Sidebar.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\UserList.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\ChatArea.tsx", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\services\\socket.ts", ["108"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\services\\api.ts", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\NotificationSystem.tsx", ["109"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\useTyping.ts", ["110", "111"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\hooks\\useSocket.ts", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\utils\\index.ts", [], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\UserSearchDialog.tsx", ["112", "113"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\MessageInput.tsx", ["114"], [], "E:\\My MERN Projects\\funnecto\\client\\src\\components\\CreateRoomDialog.tsx", ["115", "116", "117", "118"], [], {"ruleId": "119", "severity": 1, "message": "120", "line": 332, "column": 13, "nodeType": "121", "messageId": "122", "endLine": 332, "endColumn": 24}, {"ruleId": "119", "severity": 1, "message": "123", "line": 2, "column": 31, "nodeType": "121", "messageId": "122", "endLine": 2, "endColumn": 43}, {"ruleId": "119", "severity": 1, "message": "124", "line": 24, "column": 19, "nodeType": "121", "messageId": "122", "endLine": 24, "endColumn": 23}, {"ruleId": "125", "severity": 1, "message": "126", "line": 11, "column": 22, "nodeType": "121", "endLine": 11, "endColumn": 33}, {"ruleId": "125", "severity": 1, "message": "127", "line": 53, "column": 39, "nodeType": "121", "endLine": 53, "endColumn": 46}, {"ruleId": "119", "severity": 1, "message": "128", "line": 25, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 25, "endColumn": 20}, {"ruleId": "125", "severity": 1, "message": "129", "line": 69, "column": 6, "nodeType": "130", "endLine": 69, "endColumn": 19, "suggestions": "131"}, {"ruleId": "119", "severity": 1, "message": "132", "line": 9, "column": 3, "nodeType": "121", "messageId": "122", "endLine": 9, "endColumn": 7}, {"ruleId": "119", "severity": 1, "message": "133", "line": 22, "column": 10, "nodeType": "121", "messageId": "122", "endLine": 22, "endColumn": 21}, {"ruleId": "119", "severity": 1, "message": "134", "line": 35, "column": 7, "nodeType": "121", "messageId": "122", "endLine": 35, "endColumn": 13}, {"ruleId": "119", "severity": 1, "message": "135", "line": 70, "column": 20, "nodeType": "121", "messageId": "122", "endLine": 70, "endColumn": 33}, {"ruleId": "119", "severity": 1, "message": "136", "line": 92, "column": 9, "nodeType": "121", "messageId": "122", "endLine": 92, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'messageData' is assigned a value but never used.", "Identifier", "unusedVar", "'SocketEvents' is defined but never used.", "'User' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "The ref value 'typingTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'typingTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'PersonAddOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'debouncedSearch'. Either include it or remove the dependency array.", "ArrayExpression", ["137"], "'Chip' is defined but never used.", "'yupResolver' is defined but never used.", "'schema' is assigned a value but never used.", "'loadUserRooms' is assigned a value but never used.", "'watchIsPublic' is assigned a value but never used.", {"desc": "138", "fix": "139"}, "Update the dependencies array to be: [debouncedSearch, searchQuery]", {"range": "140", "text": "141"}, [1643, 1656], "[debounced<PERSON><PERSON><PERSON>, searchQ<PERSON>y]"]