import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Typography,
  Badge,
  Chip,
  Divider,
} from '@mui/material';

import { useChatStore } from '../context/store';
import { generateInitials, generateAvatarColor, getStatusColor } from '../utils';
import { User } from '../types';

const UserList: React.FC = () => {
  const { 
    selectedRoom, 
    onlineUsers, 
    setSelectedConversation 
  } = useChatStore();

  const handleUserClick = (user: User) => {
    setSelectedConversation(user);
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      case 'offline':
      default:
        return 'Offline';
    }
  };

  const renderUserItem = (user: User) => (
    <ListItem key={user.id} disablePadding>
      <ListItemButton
        onClick={() => handleUserClick(user)}
        sx={{
          '&:hover': {
            bgcolor: 'action.hover',
          },
        }}
      >
        <ListItemAvatar>
          <Badge
            overlap="circular"
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            variant="dot"
            sx={{
              '& .MuiBadge-badge': {
                backgroundColor: getStatusColor(user.status),
                color: getStatusColor(user.status),
                '&::after': {
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  borderRadius: '50%',
                  animation: user.status === 'online' ? 'ripple 1.2s infinite ease-in-out' : 'none',
                  border: '1px solid currentColor',
                  content: '""',
                },
              },
              '@keyframes ripple': {
                '0%': {
                  transform: 'scale(.8)',
                  opacity: 1,
                },
                '100%': {
                  transform: 'scale(2.4)',
                  opacity: 0,
                },
              },
            }}
          >
            <Avatar
              sx={{
                bgcolor: generateAvatarColor(user.username),
                width: 36,
                height: 36,
              }}
              src={user.avatar}
            >
              {!user.avatar && generateInitials(user.username)}
            </Avatar>
          </Badge>
        </ListItemAvatar>
        <ListItemText
          primary={
            <Typography variant="body2" noWrap>
              {user.username}
            </Typography>
          }
          secondary={
            <Chip
              label={getStatusText(user.status)}
              size="small"
              sx={{
                height: 16,
                fontSize: '0.6875rem',
                bgcolor: getStatusColor(user.status),
                color: 'white',
                '& .MuiChip-label': {
                  px: 1,
                },
              }}
            />
          }
        />
      </ListItemButton>
    </ListItem>
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" gutterBottom>
          {selectedRoom ? 'Room Members' : 'Online Users'}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {selectedRoom
            ? `${selectedRoom.members?.length || 0} members`
            : `${onlineUsers.length} online`
          }
        </Typography>
      </Box>

      {/* User List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {selectedRoom ? (
          // Show room members
          <Box>
            {selectedRoom.members && selectedRoom.members.length > 0 ? (
              <List sx={{ py: 0 }}>
                {selectedRoom.members
                  .filter(member => member.isActive)
                  .map((member) => (
                    <React.Fragment key={member.user.id}>
                      {renderUserItem(member.user)}
                      {member.role !== 'member' && (
                        <Box sx={{ px: 2, pb: 1 }}>
                          <Chip
                            label={member.role}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.6875rem', height: 20 }}
                          />
                        </Box>
                      )}
                    </React.Fragment>
                  ))}
              </List>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No members in this room
                </Typography>
              </Box>
            )}
          </Box>
        ) : (
          // Show online users
          <Box>
            {onlineUsers.length > 0 ? (
              <List sx={{ py: 0 }}>
                {/* Online Users */}
                <Box sx={{ px: 2, py: 1 }}>
                  <Typography variant="caption" color="text.secondary" fontWeight="bold">
                    ONLINE — {onlineUsers.filter(u => u.status === 'online').length}
                  </Typography>
                </Box>
                {onlineUsers
                  .filter(user => user.status === 'online')
                  .map(renderUserItem)}

                {/* Away Users */}
                {onlineUsers.some(u => u.status === 'away') && (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ px: 2, py: 1 }}>
                      <Typography variant="caption" color="text.secondary" fontWeight="bold">
                        AWAY — {onlineUsers.filter(u => u.status === 'away').length}
                      </Typography>
                    </Box>
                    {onlineUsers
                      .filter(user => user.status === 'away')
                      .map(renderUserItem)}
                  </>
                )}

                {/* Busy Users */}
                {onlineUsers.some(u => u.status === 'busy') && (
                  <>
                    <Divider sx={{ my: 1 }} />
                    <Box sx={{ px: 2, py: 1 }}>
                      <Typography variant="caption" color="text.secondary" fontWeight="bold">
                        BUSY — {onlineUsers.filter(u => u.status === 'busy').length}
                      </Typography>
                    </Box>
                    {onlineUsers
                      .filter(user => user.status === 'busy')
                      .map(renderUserItem)}
                  </>
                )}
              </List>
            ) : (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  No users online
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Users will appear here when they come online
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default UserList;
