{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link areIntervalsOverlapping} function options.\n */\n\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping unless `inclusive` is set to `true`.\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n * @param options - The object with options\n *\n * @returns Whether the time intervals are overlapping\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\nexport function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  const [leftStartTime, leftEndTime] = [+toDate(intervalLeft.start, options?.in), +toDate(intervalLeft.end, options?.in)].sort((a, b) => a - b);\n  const [rightStartTime, rightEndTime] = [+toDate(intervalRight.start, options?.in), +toDate(intervalRight.end, options?.in)].sort((a, b) => a - b);\n  if (options?.inclusive) return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}\n\n// Fallback for modularized imports:\nexport default areIntervalsOverlapping;", "map": {"version": 3, "names": ["toDate", "areIntervalsOverlapping", "intervalLeft", "intervalRight", "options", "leftStartTime", "leftEndTime", "start", "in", "end", "sort", "a", "b", "rightStartTime", "rightEndTime", "inclusive"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/date-fns/areIntervalsOverlapping.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link areIntervalsOverlapping} function options.\n */\n\n/**\n * @name areIntervalsOverlapping\n * @category Interval Helpers\n * @summary Is the given time interval overlapping with another time interval?\n *\n * @description\n * Is the given time interval overlapping with another time interval? Adjacent intervals do not count as overlapping unless `inclusive` is set to `true`.\n *\n * @param intervalLeft - The first interval to compare.\n * @param intervalRight - The second interval to compare.\n * @param options - The object with options\n *\n * @returns Whether the time intervals are overlapping\n *\n * @example\n * // For overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 17), end: new Date(2014, 0, 21) }\n * )\n * //=> true\n *\n * @example\n * // For non-overlapping time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 21), end: new Date(2014, 0, 22) }\n * )\n * //=> false\n *\n * @example\n * // For adjacent time intervals:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 30) }\n * )\n * //=> false\n *\n * @example\n * // Using the inclusive option:\n * areIntervalsOverlapping(\n *   { start: new Date(2014, 0, 10), end: new Date(2014, 0, 20) },\n *   { start: new Date(2014, 0, 20), end: new Date(2014, 0, 24) },\n *   { inclusive: true }\n * )\n * //=> true\n */\nexport function areIntervalsOverlapping(intervalLeft, intervalRight, options) {\n  const [leftStartTime, leftEndTime] = [\n    +toDate(intervalLeft.start, options?.in),\n    +toDate(intervalLeft.end, options?.in),\n  ].sort((a, b) => a - b);\n  const [rightStartTime, rightEndTime] = [\n    +toDate(intervalRight.start, options?.in),\n    +toDate(intervalRight.end, options?.in),\n  ].sort((a, b) => a - b);\n\n  if (options?.inclusive)\n    return leftStartTime <= rightEndTime && rightStartTime <= leftEndTime;\n\n  return leftStartTime < rightEndTime && rightStartTime < leftEndTime;\n}\n\n// Fallback for modularized imports:\nexport default areIntervalsOverlapping;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAE;EAC5E,MAAM,CAACC,aAAa,EAAEC,WAAW,CAAC,GAAG,CACnC,CAACN,MAAM,CAACE,YAAY,CAACK,KAAK,EAAEH,OAAO,EAAEI,EAAE,CAAC,EACxC,CAACR,MAAM,CAACE,YAAY,CAACO,GAAG,EAAEL,OAAO,EAAEI,EAAE,CAAC,CACvC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EACvB,MAAM,CAACC,cAAc,EAAEC,YAAY,CAAC,GAAG,CACrC,CAACd,MAAM,CAACG,aAAa,CAACI,KAAK,EAAEH,OAAO,EAAEI,EAAE,CAAC,EACzC,CAACR,MAAM,CAACG,aAAa,CAACM,GAAG,EAAEL,OAAO,EAAEI,EAAE,CAAC,CACxC,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAEvB,IAAIR,OAAO,EAAEW,SAAS,EACpB,OAAOV,aAAa,IAAIS,YAAY,IAAID,cAAc,IAAIP,WAAW;EAEvE,OAAOD,aAAa,GAAGS,YAAY,IAAID,cAAc,GAAGP,WAAW;AACrE;;AAEA;AACA,eAAeL,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}