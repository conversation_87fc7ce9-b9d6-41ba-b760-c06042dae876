{"ast": null, "code": "import { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = props?.theme?.unstable_sxConfig ?? defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n    sx: inSx,\n    ...other\n  } = props;\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return {\n        ...systemProps,\n        ...result\n      };\n    };\n  } else {\n    finalSx = {\n      ...systemProps,\n      ...inSx\n    };\n  }\n  return {\n    ...otherProps,\n    sx: finalSx\n  };\n}", "map": {"version": 3, "names": ["isPlainObject", "defaultSxConfig", "splitProps", "props", "result", "systemProps", "otherProps", "config", "theme", "unstable_sxConfig", "Object", "keys", "for<PERSON>ach", "prop", "extendSxProp", "sx", "inSx", "other", "finalSx", "Array", "isArray", "args"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/styleFunctionSx/extendSxProp.js"], "sourcesContent": ["import { isPlainObject } from '@mui/utils/deepmerge';\nimport defaultSxConfig from \"./defaultSxConfig.js\";\nconst splitProps = props => {\n  const result = {\n    systemProps: {},\n    otherProps: {}\n  };\n  const config = props?.theme?.unstable_sxConfig ?? defaultSxConfig;\n  Object.keys(props).forEach(prop => {\n    if (config[prop]) {\n      result.systemProps[prop] = props[prop];\n    } else {\n      result.otherProps[prop] = props[prop];\n    }\n  });\n  return result;\n};\nexport default function extendSxProp(props) {\n  const {\n    sx: inSx,\n    ...other\n  } = props;\n  const {\n    systemProps,\n    otherProps\n  } = splitProps(other);\n  let finalSx;\n  if (Array.isArray(inSx)) {\n    finalSx = [systemProps, ...inSx];\n  } else if (typeof inSx === 'function') {\n    finalSx = (...args) => {\n      const result = inSx(...args);\n      if (!isPlainObject(result)) {\n        return systemProps;\n      }\n      return {\n        ...systemProps,\n        ...result\n      };\n    };\n  } else {\n    finalSx = {\n      ...systemProps,\n      ...inSx\n    };\n  }\n  return {\n    ...otherProps,\n    sx: finalSx\n  };\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,UAAU,GAAGC,KAAK,IAAI;EAC1B,MAAMC,MAAM,GAAG;IACbC,WAAW,EAAE,CAAC,CAAC;IACfC,UAAU,EAAE,CAAC;EACf,CAAC;EACD,MAAMC,MAAM,GAAGJ,KAAK,EAAEK,KAAK,EAAEC,iBAAiB,IAAIR,eAAe;EACjES,MAAM,CAACC,IAAI,CAACR,KAAK,CAAC,CAACS,OAAO,CAACC,IAAI,IAAI;IACjC,IAAIN,MAAM,CAACM,IAAI,CAAC,EAAE;MAChBT,MAAM,CAACC,WAAW,CAACQ,IAAI,CAAC,GAAGV,KAAK,CAACU,IAAI,CAAC;IACxC,CAAC,MAAM;MACLT,MAAM,CAACE,UAAU,CAACO,IAAI,CAAC,GAAGV,KAAK,CAACU,IAAI,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOT,MAAM;AACf,CAAC;AACD,eAAe,SAASU,YAAYA,CAACX,KAAK,EAAE;EAC1C,MAAM;IACJY,EAAE,EAAEC,IAAI;IACR,GAAGC;EACL,CAAC,GAAGd,KAAK;EACT,MAAM;IACJE,WAAW;IACXC;EACF,CAAC,GAAGJ,UAAU,CAACe,KAAK,CAAC;EACrB,IAAIC,OAAO;EACX,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACvBE,OAAO,GAAG,CAACb,WAAW,EAAE,GAAGW,IAAI,CAAC;EAClC,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IACrCE,OAAO,GAAGA,CAAC,GAAGG,IAAI,KAAK;MACrB,MAAMjB,MAAM,GAAGY,IAAI,CAAC,GAAGK,IAAI,CAAC;MAC5B,IAAI,CAACrB,aAAa,CAACI,MAAM,CAAC,EAAE;QAC1B,OAAOC,WAAW;MACpB;MACA,OAAO;QACL,GAAGA,WAAW;QACd,GAAGD;MACL,CAAC;IACH,CAAC;EACH,CAAC,MAAM;IACLc,OAAO,GAAG;MACR,GAAGb,WAAW;MACd,GAAGW;IACL,CAAC;EACH;EACA,OAAO;IACL,GAAGV,UAAU;IACbS,EAAE,EAAEG;EACN,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}