{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme, parserConfig = {}) {\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(([, {\n      vars: schemeVars\n    }]) => {\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(([key, {\n      css\n    }]) => {\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;", "map": {"version": 3, "names": ["deepmerge", "cssVarsParser", "prepareCssVars", "theme", "parserConfig", "getSelector", "defaultGetSelector", "disableCssColorScheme", "colorSchemeSelector", "selector", "colorSchemes", "components", "defaultColorScheme", "otherTheme", "vars", "rootVars", "css", "rootCss", "varsWithDefaults", "rootVarsWithDefaults", "themeVars", "colorSchemesMap", "defaultScheme", "otherColorSchemes", "Object", "entries", "for<PERSON>ach", "key", "scheme", "colorScheme", "cssObject", "rule", "startsWith", "includes", "mode", "palette", "replace", "String", "generateThemeVars", "schemeVars", "generateStyleSheets", "stylesheets", "insertStyleSheet", "keys", "length", "push", "undefined", "defaultSchemeVal", "other", "cssColorSheme", "finalCss"], "sources": ["E:/My MERN Projects/funnecto/client/node_modules/@mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from \"./cssVarsParser.js\";\nfunction prepareCssVars(theme, parserConfig = {}) {\n  const {\n    getSelector = defaultGetSelector,\n    disableCssColorScheme,\n    colorSchemeSelector: selector\n  } = parserConfig;\n  // @ts-ignore - ignore components do not exist\n  const {\n    colorSchemes = {},\n    components,\n    defaultColorScheme = 'light',\n    ...otherTheme\n  } = theme;\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n    [defaultColorScheme]: defaultScheme,\n    ...otherColorSchemes\n  } = colorSchemes;\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (defaultScheme) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(defaultScheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  function defaultGetSelector(colorScheme, cssObject) {\n    let rule = selector;\n    if (selector === 'class') {\n      rule = '.%s';\n    }\n    if (selector === 'data') {\n      rule = '[data-%s]';\n    }\n    if (selector?.startsWith('data-') && !selector.includes('%s')) {\n      // 'data-joy-color-scheme' -> '[data-joy-color-scheme=\"%s\"]'\n      rule = `[${selector}=\"%s\"]`;\n    }\n    if (colorScheme) {\n      if (rule === 'media') {\n        if (theme.defaultColorScheme === colorScheme) {\n          return ':root';\n        }\n        const mode = colorSchemes[colorScheme]?.palette?.mode || colorScheme;\n        return {\n          [`@media (prefers-color-scheme: ${mode})`]: {\n            ':root': cssObject\n          }\n        };\n      }\n      if (rule) {\n        if (theme.defaultColorScheme === colorScheme) {\n          return `:root, ${rule.replace('%s', String(colorScheme))}`;\n        }\n        return rule.replace('%s', String(colorScheme));\n      }\n    }\n    return ':root';\n  }\n  const generateThemeVars = () => {\n    let vars = {\n      ...rootVars\n    };\n    Object.entries(colorSchemesMap).forEach(([, {\n      vars: schemeVars\n    }]) => {\n      vars = deepmerge(vars, schemeVars);\n    });\n    return vars;\n  };\n  const generateStyleSheets = () => {\n    const stylesheets = [];\n    const colorScheme = theme.defaultColorScheme || 'light';\n    function insertStyleSheet(key, css) {\n      if (Object.keys(css).length) {\n        stylesheets.push(typeof key === 'string' ? {\n          [key]: {\n            ...css\n          }\n        } : key);\n      }\n    }\n    insertStyleSheet(getSelector(undefined, {\n      ...rootCss\n    }), rootCss);\n    const {\n      [colorScheme]: defaultSchemeVal,\n      ...other\n    } = colorSchemesMap;\n    if (defaultSchemeVal) {\n      // default color scheme has to come before other color schemes\n      const {\n        css\n      } = defaultSchemeVal;\n      const cssColorSheme = colorSchemes[colorScheme]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(colorScheme, {\n        ...finalCss\n      }), finalCss);\n    }\n    Object.entries(other).forEach(([key, {\n      css\n    }]) => {\n      const cssColorSheme = colorSchemes[key]?.palette?.mode;\n      const finalCss = !disableCssColorScheme && cssColorSheme ? {\n        colorScheme: cssColorSheme,\n        ...css\n      } : {\n        ...css\n      };\n      insertStyleSheet(getSelector(key, {\n        ...finalCss\n      }), finalCss);\n    });\n    return stylesheets;\n  };\n  return {\n    vars: themeVars,\n    generateThemeVars,\n    generateStyleSheets\n  };\n}\nexport default prepareCssVars;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,cAAcA,CAACC,KAAK,EAAEC,YAAY,GAAG,CAAC,CAAC,EAAE;EAChD,MAAM;IACJC,WAAW,GAAGC,kBAAkB;IAChCC,qBAAqB;IACrBC,mBAAmB,EAAEC;EACvB,CAAC,GAAGL,YAAY;EAChB;EACA,MAAM;IACJM,YAAY,GAAG,CAAC,CAAC;IACjBC,UAAU;IACVC,kBAAkB,GAAG,OAAO;IAC5B,GAAGC;EACL,CAAC,GAAGV,KAAK;EACT,MAAM;IACJW,IAAI,EAAEC,QAAQ;IACdC,GAAG,EAAEC,OAAO;IACZC,gBAAgB,EAAEC;EACpB,CAAC,GAAGlB,aAAa,CAACY,UAAU,EAAET,YAAY,CAAC;EAC3C,IAAIgB,SAAS,GAAGD,oBAAoB;EACpC,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAM;IACJ,CAACT,kBAAkB,GAAGU,aAAa;IACnC,GAAGC;EACL,CAAC,GAAGb,YAAY;EAChBc,MAAM,CAACC,OAAO,CAACF,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;IACjE,MAAM;MACJd,IAAI;MACJE,GAAG;MACHE;IACF,CAAC,GAAGjB,aAAa,CAAC2B,MAAM,EAAExB,YAAY,CAAC;IACvCgB,SAAS,GAAGpB,SAAS,CAACoB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACM,GAAG,CAAC,GAAG;MACrBX,GAAG;MACHF;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIQ,aAAa,EAAE;IACjB;IACA,MAAM;MACJN,GAAG;MACHF,IAAI;MACJI;IACF,CAAC,GAAGjB,aAAa,CAACqB,aAAa,EAAElB,YAAY,CAAC;IAC9CgB,SAAS,GAAGpB,SAAS,CAACoB,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACT,kBAAkB,CAAC,GAAG;MACpCI,GAAG;MACHF;IACF,CAAC;EACH;EACA,SAASR,kBAAkBA,CAACuB,WAAW,EAAEC,SAAS,EAAE;IAClD,IAAIC,IAAI,GAAGtB,QAAQ;IACnB,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxBsB,IAAI,GAAG,KAAK;IACd;IACA,IAAItB,QAAQ,KAAK,MAAM,EAAE;MACvBsB,IAAI,GAAG,WAAW;IACpB;IACA,IAAItB,QAAQ,EAAEuB,UAAU,CAAC,OAAO,CAAC,IAAI,CAACvB,QAAQ,CAACwB,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC7D;MACAF,IAAI,GAAG,IAAItB,QAAQ,QAAQ;IAC7B;IACA,IAAIoB,WAAW,EAAE;MACf,IAAIE,IAAI,KAAK,OAAO,EAAE;QACpB,IAAI5B,KAAK,CAACS,kBAAkB,KAAKiB,WAAW,EAAE;UAC5C,OAAO,OAAO;QAChB;QACA,MAAMK,IAAI,GAAGxB,YAAY,CAACmB,WAAW,CAAC,EAAEM,OAAO,EAAED,IAAI,IAAIL,WAAW;QACpE,OAAO;UACL,CAAC,iCAAiCK,IAAI,GAAG,GAAG;YAC1C,OAAO,EAAEJ;UACX;QACF,CAAC;MACH;MACA,IAAIC,IAAI,EAAE;QACR,IAAI5B,KAAK,CAACS,kBAAkB,KAAKiB,WAAW,EAAE;UAC5C,OAAO,UAAUE,IAAI,CAACK,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACR,WAAW,CAAC,CAAC,EAAE;QAC5D;QACA,OAAOE,IAAI,CAACK,OAAO,CAAC,IAAI,EAAEC,MAAM,CAACR,WAAW,CAAC,CAAC;MAChD;IACF;IACA,OAAO,OAAO;EAChB;EACA,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIxB,IAAI,GAAG;MACT,GAAGC;IACL,CAAC;IACDS,MAAM,CAACC,OAAO,CAACJ,eAAe,CAAC,CAACK,OAAO,CAAC,CAAC,GAAG;MAC1CZ,IAAI,EAAEyB;IACR,CAAC,CAAC,KAAK;MACLzB,IAAI,GAAGd,SAAS,CAACc,IAAI,EAAEyB,UAAU,CAAC;IACpC,CAAC,CAAC;IACF,OAAOzB,IAAI;EACb,CAAC;EACD,MAAM0B,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMZ,WAAW,GAAG1B,KAAK,CAACS,kBAAkB,IAAI,OAAO;IACvD,SAAS8B,gBAAgBA,CAACf,GAAG,EAAEX,GAAG,EAAE;MAClC,IAAIQ,MAAM,CAACmB,IAAI,CAAC3B,GAAG,CAAC,CAAC4B,MAAM,EAAE;QAC3BH,WAAW,CAACI,IAAI,CAAC,OAAOlB,GAAG,KAAK,QAAQ,GAAG;UACzC,CAACA,GAAG,GAAG;YACL,GAAGX;UACL;QACF,CAAC,GAAGW,GAAG,CAAC;MACV;IACF;IACAe,gBAAgB,CAACrC,WAAW,CAACyC,SAAS,EAAE;MACtC,GAAG7B;IACL,CAAC,CAAC,EAAEA,OAAO,CAAC;IACZ,MAAM;MACJ,CAACY,WAAW,GAAGkB,gBAAgB;MAC/B,GAAGC;IACL,CAAC,GAAG3B,eAAe;IACnB,IAAI0B,gBAAgB,EAAE;MACpB;MACA,MAAM;QACJ/B;MACF,CAAC,GAAG+B,gBAAgB;MACpB,MAAME,aAAa,GAAGvC,YAAY,CAACmB,WAAW,CAAC,EAAEM,OAAO,EAAED,IAAI;MAC9D,MAAMgB,QAAQ,GAAG,CAAC3C,qBAAqB,IAAI0C,aAAa,GAAG;QACzDpB,WAAW,EAAEoB,aAAa;QAC1B,GAAGjC;MACL,CAAC,GAAG;QACF,GAAGA;MACL,CAAC;MACD0B,gBAAgB,CAACrC,WAAW,CAACwB,WAAW,EAAE;QACxC,GAAGqB;MACL,CAAC,CAAC,EAAEA,QAAQ,CAAC;IACf;IACA1B,MAAM,CAACC,OAAO,CAACuB,KAAK,CAAC,CAACtB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAE;MACnCX;IACF,CAAC,CAAC,KAAK;MACL,MAAMiC,aAAa,GAAGvC,YAAY,CAACiB,GAAG,CAAC,EAAEQ,OAAO,EAAED,IAAI;MACtD,MAAMgB,QAAQ,GAAG,CAAC3C,qBAAqB,IAAI0C,aAAa,GAAG;QACzDpB,WAAW,EAAEoB,aAAa;QAC1B,GAAGjC;MACL,CAAC,GAAG;QACF,GAAGA;MACL,CAAC;MACD0B,gBAAgB,CAACrC,WAAW,CAACsB,GAAG,EAAE;QAChC,GAAGuB;MACL,CAAC,CAAC,EAAEA,QAAQ,CAAC;IACf,CAAC,CAAC;IACF,OAAOT,WAAW;EACpB,CAAC;EACD,OAAO;IACL3B,IAAI,EAAEM,SAAS;IACfkB,iBAAiB;IACjBE;EACF,CAAC;AACH;AACA,eAAetC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}